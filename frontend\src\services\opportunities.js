const NEW_API_URL = process.env.REACT_APP_API_BASE_URL;

export const fetchOpportunities = async (locationId, pipelineIds, locationAccessToken) => {
    try {
      const opportunitiesRes = await fetch(
        `${NEW_API_URL}/api/opportunities/search?location_id=${locationId}&pipelineIds=${pipelineIds.join(",")}`,
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${locationAccessToken}`,
          },
        }
      );
  
      if (!opportunitiesRes.ok) {
        console.error("Failed to fetch opportunities", opportunitiesRes.status);
        return null;
      }
  
      // Parse response
      const data = await opportunitiesRes.json();
      console.log("opportunities response data:", data);
      
      return data; // Return the raw data for processing elsewhere
    } catch (error) {
      console.error("Error fetching opportunities:", error);
      return null;
    }
  };
  
  /* 
   * opportunitiesProcessor.js - Process and organize opportunities data
   */
  
  // Main function to process opportunities data
  export const processOpportunitiesData = (data, pipelines) => {
    if (!data) {
      console.error("No data to process");
      return [];
    }
    
    // Log the received data structure
    console.log("Processing data structure:", {
      hasDataProp: !!data.data,
      dataIsArray: data.data && Array.isArray(data.data),
      dataLength: data.data && Array.isArray(data.data) ? data.data.length : 'N/A'
    });
  
    let organizedData = [];
    
    // Check various possible data structures
    if (data.data && Array.isArray(data.data)) {
      // Check if this is an array of pipeline data or something else
      const firstItem = data.data[0];
      if (firstItem && firstItem.pipelineId && (firstItem.stages || firstItem.opportunities)) {
        // This is likely pipeline-structured data
        organizedData = processPipelineArrayResponse(data.data, pipelines);
      } else {
        // This might be a flat array of opportunities
        organizedData = processFlatOpportunitiesResponse(data.data, pipelines);
      }
    } else if (data.data && data.data.opportunities && Array.isArray(data.data.opportunities)) {
      // This appears to be a single object with an opportunities array
      organizedData = processFlatOpportunitiesResponse(data.data.opportunities, pipelines);
    } else if (Array.isArray(data)) {
      // This might be a direct array of opportunities
      organizedData = processFlatOpportunitiesResponse(data, pipelines);
    } else if (data.opportunities && Array.isArray(data.opportunities)) {
      // Direct object with opportunities array
      organizedData = processFlatOpportunitiesResponse(data.opportunities, pipelines);
    } else {
      console.error("Unexpected opportunities response format", data);
      return [];
    }
  
    return organizedData;
  };
  
  // Process a response where data is an array of pipeline objects
  const processPipelineArrayResponse = (pipelineDataArray, pipelines) => {
    if (!Array.isArray(pipelineDataArray)) {
      console.error("Expected array of pipeline data but got:", pipelineDataArray);
      return [];
    }
  
    const organizedData = pipelineDataArray.map(pipelineData => {
      // Find matching pipeline from our pipelines list
      const pipeline = pipelines.find(p => p.id === pipelineData.pipelineId);
      
      return {
        pipelineId: pipelineData.pipelineId,
        pipelineName: pipeline?.name || pipelineData.pipelineName || "Unknown Pipeline",
        stages: Array.isArray(pipelineData.stages)
          ? pipelineData.stages.map(stage => ({
              stageId: stage.stageId,
              stageName: stage.stageName || "Unknown Stage",
              opportunities: Array.isArray(stage.opportunities)
                ? stage.opportunities.map(opportunity => ({
                    ...opportunity,
                    pipelineId: pipelineData.pipelineId,
                    pipelineName: pipeline?.name || pipelineData.pipelineName || "Unknown Pipeline",
                    stageId: stage.stageId,
                    stageName: stage.stageName || "Unknown Stage"
                  }))
                : []
            }))
          : []
      };
    });
  
    logOrganizedData(organizedData);
    return organizedData;
  };
  
  // Process a flat array of opportunities and organize by pipeline/stage
  const processFlatOpportunitiesResponse = (opportunities, pipelines) => {
    if (!Array.isArray(opportunities)) {
      console.error("Expected array of opportunities but got:", opportunities);
      return [];
    }
  
    // Group opportunities by pipeline
    const opportunitiesByPipeline = {};
    
    opportunities.forEach(opportunity => {
      const pipelineId = opportunity.pipelineId;
      const stageId = opportunity.stageId;
      
      if (!pipelineId || !stageId) {
        console.warn("Opportunity missing pipelineId or stageId:", opportunity);
        return;
      }
      
      // Initialize pipeline if not exists
      if (!opportunitiesByPipeline[pipelineId]) {
        const pipeline = pipelines.find(p => p.id === pipelineId);
        opportunitiesByPipeline[pipelineId] = {
          pipelineId,
          pipelineName: pipeline?.name || "Unknown Pipeline",
          stages: {}
        };
      }
      
      // Initialize stage if not exists
      if (!opportunitiesByPipeline[pipelineId].stages[stageId]) {
        opportunitiesByPipeline[pipelineId].stages[stageId] = {
          stageId,
          stageName: opportunity.stageName || "Unknown Stage",
          opportunities: []
        };
      }
      
      // Add opportunity to stage with additional context
      opportunitiesByPipeline[pipelineId].stages[stageId].opportunities.push({
        ...opportunity,
        pipelineName: opportunitiesByPipeline[pipelineId].pipelineName,
        stageName: opportunitiesByPipeline[pipelineId].stages[stageId].stageName
      });
    });
    
    // Convert to expected format
    const organizedData = Object.values(opportunitiesByPipeline).map(pipeline => {
      return {
        ...pipeline,
        stages: Object.values(pipeline.stages)
      };
    });
  
    logOrganizedData(organizedData);
    return organizedData;
  };
  
  // Helper function to log the organized data for debugging
  const logOrganizedData = (organizedData) => {
    console.log("Organized Pipeline Data:", organizedData);
    
    organizedData.forEach(pipeline => {
      console.log(`Pipeline: ${pipeline.pipelineName} (${pipeline.pipelineId})`);
      pipeline.stages.forEach(stage => {
        console.log(`  Stage: ${stage.stageName} (${stage.stageId})`);
        console.log(`  Number of opportunities: ${stage.opportunities.length}`);
        if (stage.opportunities.length > 0) {
          console.log(`  Sample opportunity: ${JSON.stringify(stage.opportunities[0].name || stage.opportunities[0].title)}`);
        }
      });
    });
  };
  