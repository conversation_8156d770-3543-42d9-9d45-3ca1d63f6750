const express = require("express");

const cors = require("cors");

const bodyParser = require("body-parser");

const jwt = require("jsonwebtoken");

const bcrypt = require("bcryptjs");

const router = express.Router();

const app = express();

const JWT_SECRET = "your-secret-key"; // In production, use environment variables

// Middleware

app.use(cors());

app.use(bodyParser.json());

// In-memory user storage (replace with localStorage in frontend)

let users = [
  {
    id: "1",

    name: "Test User",

    email: "<EMAIL>",

    // In a real application, never store plain text passwords

    // This would be a hashed password: bcrypt.hashSync('password123', 10)

    password: "123",
  },
];

// Auth middleware

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers["authorization"];

  const token = authHeader && authHeader.split(" ")[1];

  if (!token) return res.status(401).json({ message: "Unauthorized" });

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) return res.status(403).json({ message: "Invalid token" });

    req.user = user;

    next();
  });
};

// Routes

router.post("/register", (req, res) => {
  const { name, email, password } = req.body;

  // Validate input

  if (!name || !email || !password) {
    return res.status(400).json({ message: "All fields are required" });
  }

  // Check if user already exists

  if (users.find((user) => user.email === email)) {
    return res.status(400).json({ message: "User already exists" });
  }

  // In a production app, hash the password

  // const hashedPassword = bcrypt.hashSync(password, 10);

  const newUser = {
    id: Date.now().toString(),

    name,

    email,

    password, // In production: hashedPassword
  };

  users.push(newUser);

  res.status(201).json({ message: "User registered successfully" });
});

router.post("/login", (req, res) => {
  const { email, password } = req.body;

  // Find user

  const user = users.find((user) => user.email == email);

  if (!user) {
    return res.status(404).json({ message: "User not found" });
  }

  // In production, check password with bcrypt.compareSync(password, user.password)

  const isPasswordValid = password === user.password;

  if (!isPasswordValid) {
    return res.status(401).json({ message: "Invalid credentials" });
  }

  // Generate JWT token

  const token = jwt.sign(
    { id: user.id, email: user.email, name: user.name },

    JWT_SECRET,

    { expiresIn: "24h" }
  );

  res.json({
    message: "Login successful",

    token,

    user: {
      id: user.id,

      name: user.name,

      email: user.email,
    },
  });
});

// Protected route example

router.get("/profile", authenticateToken, (req, res) => {
  res.json({ user: req.user });
});

module.exports = router;
