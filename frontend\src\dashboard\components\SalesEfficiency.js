import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Select,
  MenuItem,
  FormControl,
  IconButton,
  useTheme,
  TextField,
  Skeleton,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import ShareOutlinedIcon from "@mui/icons-material/ShareOutlined";
import AccessTimeOutlinedIcon from "@mui/icons-material/AccessTimeOutlined";
import AssessmentOutlinedIcon from "@mui/icons-material/AssessmentOutlined";
import TrendingUpOutlinedIcon from "@mui/icons-material/TrendingUpOutlined";
import CheckIcon from "@mui/icons-material/Check";
import { useParams } from "react-router";

function formatDuration(seconds) {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;
  const unitStyle = { fontSize: "0.9rem" }; // or "0.75rem" for even smaller

  if (h > 0) {
    return (
      <>
        {h}
        <span style={unitStyle}>h</span> {m}
        <span style={unitStyle}>m</span> {s}
        <span style={unitStyle}>s</span>
      </>
    );
  } else if (m > 0) {
    return (
      <>
        {m}
        <span style={unitStyle}>m</span> {s}
        <span style={unitStyle}>s</span>
      </>
    );
  } else {
    return (
      <>
        {s}
        <span style={unitStyle}>s</span>
      </>
    );
  }
}

export default function SalesEfficiency() {
  const theme = useTheme();
  const [selectedUser, setSelectedUser] = useState("all");
  const [salesData, setSalesData] = useState({
    averageSalesDuration: 0,
    totalRevenue: 0,
    salesVelocity: 0,
  });
  const handleUserChange = (event) => {
    setSelectedUser(event.target.value);
  };
  const metrics = [
    {
      label: "Average Sales Duration",
      value: formatDuration(salesData.averageSalesDuration),
      change: "0%",
      icon: <AccessTimeOutlinedIcon sx={{ fontSize: 20 }} />,
    },
    {
      label: "Total Sale Value",
      value: `$${salesData.totalRevenue.toLocaleString()}`,
      change: "0%",
      icon: <AssessmentOutlinedIcon sx={{ fontSize: 20 }} />,
    },
    {
      label: "Sales Velocity",
      value: (
        <>
          ${salesData.salesVelocity.toLocaleString()}/
          <span style={{ fontSize: "0.9rem",fontWeight:'500' }}>Month</span>
        </>
      ),
      change: "0%",
      icon: <TrendingUpOutlinedIcon sx={{ fontSize: 20 }} />,
    },
  ];
  const { locationId } = useParams(); // React Router v6+
  const [users, setUsers] = useState([]);
  const NEW_API_URL = process.env.REACT_APP_NEW_BASE_URL;
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  useEffect(() => {
    const token = localStorage.getItem("locationAccessTokenContact");
    if (!token || !locationId) return;
    fetch(`${NEW_API_URL}/api/users-by-location?locationId=${locationId}`, {
      headers: {
        Authorization: token,
      },
    })
      .then((res) => res.json())
      .then((data) => {
        console.log(data);
        setUsers(data.users || []);
      })
      .catch((err) => {
        console.error("Failed to fetch conversions total:", err);
      });
  }, [locationId]);

  useEffect(() => {
    const token = localStorage.getItem("locationAccessTokenContact");
    if (!token || !locationId) return;

    setLoading(true);
    setError(false);

    let url = `http://localhost:5000/api/sales-efficiency?locationId=${locationId}&status=won`;
    if (selectedUser !== "all") {
      url += `&userId=${selectedUser}`;
    }

    fetch(url, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
      .then(async (res) => {
        if (res.status !== 200) {
          setError(true);
          setLoading(false);
          return;
        }
        const data = await res.json();
        setSalesData({
          averageSalesDuration: data.averageSalesDuration || 0,
          totalRevenue: data.totalRevenue || 0,
          salesVelocity: data.salesVelocity || 0,
        });
        setLoading(false);
      })
      .catch((err) => {
        setError(true);
        setLoading(false);
        setSalesData({
          averageSalesDuration: 0,
          totalRevenue: 0,
          salesVelocity: 0,
        });
      });
  }, [locationId, selectedUser]);

  const handleShare = async () => {
    const shareData = {
      title: "Sales Report",
      text: "Hello world", // You can change this later to dynamic content
      // url: window.location.href, // Optional: current page URL
    };

    try {
      if (navigator.share) {
        await navigator.share(shareData);
        console.log("Shared successfully");
      } else {
        alert("Share not supported on this device/browser.");
      }
    } catch (error) {
      console.error("Error sharing:", error);
    }
  };

  return (
    <Card
      sx={{
        backgroundColor: (theme) =>
          theme.palette.mode === "dark" ? "#0f131b" : "#ffffff",
        boxShadow:
          theme.palette.mode === "dark"
            ? "0 1px 3px rgba(0,0,0,0.5)"
            : "0 1px 3px rgba(0,0,0,0.12)",
        borderRadius: 2,
      }}
    >
      <CardContent>
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 4,
          }}
        >
          <Typography component="h2" variant="subtitle2" fontWeight="bold" gutterBottom>
            Sales Efficiency
          </Typography>
          {/* <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <FormControl
              size="small"
              sx={{
                minWidth: 120,
                "& .MuiOutlinedInput-root": {
                  backgroundColor:
                    theme.palette.mode === "dark"
                      ? theme.palette.grey[800]
                      : "#f5f5f5",
                  "&:hover": {
                    backgroundColor:
                      theme.palette.mode === "dark"
                        ? theme.palette.grey[700]
                        : "#eeeeee",
                  },
                },
              }}
            >
              <Select
                value={selectedUser}
                onChange={handleUserChange}
                displayEmpty
                renderValue={(value) => {
                  if (value === "all") return "All Users";
                  const user = users?.find((u) => u.id === value);
                  return user?.name || "All Users";
                }}
                MenuProps={{
                  PaperProps: {
                    style: {
                      maxHeight: 250,
                    },
                  },
                }}
                sx={{
                  fontSize: "0.875rem",
                  "& .MuiSelect-select": {
                    py: 1,
                  },
                }}
              >
                <MenuItem value="all">
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      width: "100%",
                    }}
                  >
                    <Box sx={{ flexGrow: 1 }}>All Users</Box>
                    {selectedUser === "all" && (
                      <CheckIcon
                        sx={{ fontSize: 18, color: theme.palette.primary.main }}
                      />
                    )}
                  </Box>
                </MenuItem>
                {users?.map((user) => (
                  <MenuItem key={user.id} value={user.id}>
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        width: "100%",
                      }}
                    >
                      <Box sx={{ flexGrow: 1 }}>{user.name}</Box>
                      {selectedUser === user.id && (
                        <CheckIcon
                          sx={{
                            fontSize: 18,
                            color: theme.palette.primary.main,
                          }}
                        />
                      )}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <IconButton
              size="small"
              onClick={handleShare}
              sx={{
                color: theme.palette.text.secondary,
                "&:hover": {
                  backgroundColor:
                    theme.palette.mode === "dark"
                      ? theme.palette.grey[800]
                      : theme.palette.grey[100],
                },
              }}
            >
              <ShareOutlinedIcon fontSize="small" />
            </IconButton>
          </Box> */}
        </Box>
        {/* Metrics Grid */}
        {loading ? (
          <Grid container mt={3} spacing={3}>
            {[0, 1, 2].map((_, idx) => (
              <Grid item size={{ xs: 12, sm: 4 }} key={idx}>
                <Skeleton variant="rectangular" height={100} sx={{ borderRadius: 2 }} />
              </Grid>
            ))}
          </Grid>
        ) : error ? (
          <Box sx={{ mt: 3, color: "error.main", textAlign: "center" }}>
            <Typography variant="h6">GHL API's Not working</Typography>
          </Box>
        ) : (
          <Grid container mt={3} spacing={3}>
            {metrics.map((metric, index) => (
              <Grid item size={{ xs: 12, sm: 4 }} key={index}>
                <Box
                  sx={{
                    backgroundColor:
                      theme.palette.mode === "dark" ? "#222732" : "#f2f7fa",
                    borderRadius: 2,
                    p: 2.5,
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                    gap: 1,
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <Box
                      sx={{
                        color: theme.palette.text.secondary,
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      {metric.icon}
                    </Box>
                    <Typography
                      variant="body2"
                      sx={{
                        color: theme.palette.text.secondary,
                        fontSize: "0.75rem",
                        fontWeight: 500,
                      }}
                    >
                      {metric.label}
                    </Typography>
                  </Box>

                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 600,
                      color: theme.palette.text.primary,
                      fontSize: "2rem",
                    }}
                  >
                    {metric.value}
                  </Typography>

                  {/* <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                    <Typography
                      variant="body2"
                      sx={{
                        color: "#4caf50",
                        fontSize: "0.875rem",
                        fontWeight: 500,
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      ↑ {metric.change}
                    </Typography>
                   </Box> */}
                </Box>
              </Grid>
            ))}
          </Grid>
        )}
      </CardContent>
    </Card>
  );
}
