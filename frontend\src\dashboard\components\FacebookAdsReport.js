import React from "react";
import { Box, Card, CardContent, Typography, useTheme } from "@mui/material";
import Grid from "@mui/material/Grid2";
import TouchAppOutlinedIcon from "@mui/icons-material/TouchAppOutlined";
import AttachMoneyOutlinedIcon from "@mui/icons-material/AttachMoneyOutlined";
import LocalOfferOutlinedIcon from "@mui/icons-material/LocalOfferOutlined";
import PercentOutlinedIcon from "@mui/icons-material/PercentOutlined";

export default function FacebookAdsReport() {
  const theme = useTheme();

  const metrics = [
    {
      label: "Total Clicks",
      value: "33.46K",
      icon: <TouchAppOutlinedIcon sx={{ fontSize: 20 }} />,
    },
    {
      label: "Total Spent",
      value: "$82.28K",
      icon: <AttachMoneyOutlinedIcon sx={{ fontSize: 20 }} />,
    },
    {
      label: "CPC",
      value: "$2.46",
      icon: <LocalOfferOutlinedIcon sx={{ fontSize: 20 }} />,
    },
    {
      label: "CTR",
      value: "1.3%",
      icon: <PercentOutlinedIcon sx={{ fontSize: 20 }} />,
    },
  ];

  return (
    <Card
      sx={{
        backgroundColor: (theme) =>
          theme.palette.mode === "dark" ? theme.palette.grey[900] : "#ffffff",
        boxShadow:
          theme.palette.mode === "dark"
            ? "0 1px 3px rgba(0,0,0,0.5)"
            : "0 1px 3px rgba(0,0,0,0.12)",
        borderRadius: 2,
      }}
    >
      <CardContent>
        {/* Header */}
        <Typography component="h2" fontWeight="bold" variant="subtitle2">
          Facebook Ads Report
        </Typography>

        {/* Metrics Grid */}
        <Grid container spacing={3} sx={{ mt: (theme) => theme.spacing(2) }}>
          {metrics.map((metric, index) => (
            <Grid item size={{ xs: 12, sm: 6, md: 6 }} key={index}>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 0.5,
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                  <Box
                    sx={{
                      color: theme.palette.text.secondary,
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    {metric.icon}
                  </Box>
                  <Typography
                    variant="body2"
                    sx={{
                      color: theme.palette.text.secondary,
                      fontSize: "0.875rem",
                      fontWeight: 400,
                    }}
                  >
                    {metric.label}
                  </Typography>
                </Box>

                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                    fontSize: "1.75rem",
                    ml: 3.5, // Align with label text
                  }}
                >
                  {metric.value}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );
}
