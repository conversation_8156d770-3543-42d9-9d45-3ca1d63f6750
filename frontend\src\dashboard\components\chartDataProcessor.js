import { format, subMonths, subYears, subDays, parseISO, startOfMonth, endOfMonth } from 'date-fns';

export const timeFilters = [
  { label: '7D', value: 7, unit: 'day' },
  { label: '1M', value: 1, unit: 'month' },
  { label: '3M', value: 3, unit: 'month' },
  { label: '6M', value: 6, unit: 'month' },
];

/**
* Process location data for charts based on dateAdded field
* @param {Array} locationData - Array of location objects
* @param {Object} selectedFilter - Selected time filter
* @returns {Object} Processed data with chartData, totalCount, and filteredData
*/
export function processLocationData(locationData, selectedFilter) {
  if (!locationData || locationData.length === 0) {
    return { chartData: [], totalCount: 0, filteredData: [] };
  }

  const now = new Date();
  let startDate;

  if (selectedFilter.unit === 'day') {
    startDate = subDays(now, selectedFilter.value);
  } else if (selectedFilter.unit === 'month') {
    startDate = subMonths(now, selectedFilter.value);
  } else {
    startDate = subYears(now, selectedFilter.value);
  }

  // Filter data based on dateAdded
  const filteredData = locationData.filter(location => {
    const dateAdded = parseISO(location.dateAdded);
    return dateAdded >= startDate && dateAdded <= now;
  });

  // Group by date for 7D, by month otherwise
  let chartData;
  if (selectedFilter.unit === 'day') {
    const dailyData = {};
    filteredData.forEach(location => {
      const dateAdded = parseISO(location.dateAdded);
      const dayKey = format(dateAdded, 'yyyy-MM-dd');
      const dayLabel = format(dateAdded, 'MMM dd, yyyy');
      if (!dailyData[dayKey]) {
        dailyData[dayKey] = {
          day: dayLabel,
          count: 0,
          date: dateAdded
        };
      }
      dailyData[dayKey].count += 1;
    });
    chartData = Object.values(dailyData)
      .sort((a, b) => a.date - b.date)
      .map(item => ({
        day: item.day,
        count: item.count,
        x: item.day,
        y: item.count
      }));
  } else {
    const monthlyData = {};
    filteredData.forEach(location => {
      const dateAdded = parseISO(location.dateAdded);
      const monthKey = format(dateAdded, 'yyyy-MM');
      const monthLabel = format(dateAdded, 'MMM yyyy');
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          month: monthLabel,
          count: 0,
          date: dateAdded
        };
      }
      monthlyData[monthKey].count += 1;
    });
    chartData = Object.values(monthlyData)
      .sort((a, b) => a.date - b.date)
      .map(item => ({
        month: item.month,
        count: item.count,
        x: item.month,
        y: item.count
      }));
  }

  return {
    chartData,
    totalCount: filteredData.length,
    filteredData
  };
}

/**
* Process location data by country
* @param {Array} locationData - Array of location objects
* @param {Object} selectedFilter - Selected time filter
* @returns {Object} Processed data grouped by country
*/
export function processLocationDataByCountry(locationData, selectedFilter) {
  const { filteredData } = processLocationData(locationData, selectedFilter);

  const countryData = {};

  filteredData.forEach(location => {
    const country = location.country || 'Unknown';
    if (!countryData[country]) {
      countryData[country] = 0;
    }
    countryData[country] += 1;
  });

  const chartData = Object.entries(countryData)
    .map(([country, count]) => ({
      id: country,
      label: country,
      value: count,
      count
    }))
    .sort((a, b) => b.value - a.value);

  return {
    chartData,
    totalCount: filteredData.length,
    filteredData
  };
}

/**
* Process location data by state
* @param {Array} locationData - Array of location objects
* @param {Object} selectedFilter - Selected time filter
* @returns {Object} Processed data grouped by state
*/
export function processLocationDataByState(locationData, selectedFilter) {
  const { filteredData } = processLocationData(locationData, selectedFilter);

  const stateData = {};

  filteredData.forEach(location => {
    const state = location.state || 'Unknown';
    if (!stateData[state]) {
      stateData[state] = 0;
    }
    stateData[state] += 1;
  });

  const chartData = Object.entries(stateData)
    .map(([state, count]) => ({
      id: state,
      label: state,
      value: count,
      count
    }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 10); // Top 10 states

  return {
    chartData,
    totalCount: filteredData.length,
    filteredData
  };
}

/**
* Get latest locations (default 20)
* @param {Array} locationData - Array of location objects
* @param {number} limit - Number of latest items to return
* @returns {Array} Latest locations sorted by dateAdded
*/
export function getLatestLocations(locationData, limit = 20) {
  if (!locationData || locationData.length === 0) {
    return [];
  }

  return locationData
    .sort((a, b) => new Date(b.dateAdded) - new Date(a.dateAdded))
    .slice(0, limit);
}

/**
* Calculate growth percentage
* @param {number} current - Current value
* @param {number} previous - Previous value
* @returns {number} Growth percentage
*/
export function calculateGrowthPercentage(current, previous) {
  if (previous === 0) return current > 0 ? 100 : 0;
  return Math.round(((current - previous) / previous) * 100);
}

/**
* Format date for display
* @param {string} dateString - ISO date string
* @returns {string} Formatted date
*/
export function formatDate(dateString) {
  return format(parseISO(dateString), 'MMM dd, yyyy');
}

/**
* Get date range for filter
* @param {Object} filter - Time filter object
* @returns {Object} Start and end dates
*/
export function getDateRange(filter) {
  const now = new Date();
  let startDate;

  if (filter.unit === 'day') {
    startDate = subDays(now, filter.value);
  } else if (filter.unit === 'month') {
    startDate = subMonths(now, filter.value);
  } else {
    startDate = subYears(now, filter.value);
  }

  return {
    startDate,
    endDate: now
  };
}