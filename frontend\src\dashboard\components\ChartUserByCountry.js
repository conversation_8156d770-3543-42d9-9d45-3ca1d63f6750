import * as React from "react";
import PropTypes from "prop-types";
import { <PERSON><PERSON>hart } from "@mui/x-charts/PieChart";
import { useDrawingArea } from "@mui/x-charts/hooks";
import { styled, useTheme } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import LinearProgress, {
  linearProgressClasses,
} from "@mui/material/LinearProgress";

import {
  IndiaFlag,
  UsaFlag,
  BrazilFlag,
  GlobeFlag,
} from "../internals/components/CustomIcons";

const StyledText = styled("text", {
  shouldForwardProp: (prop) => prop !== "variant",
})(({ theme }) => ({
  textAnchor: "middle",
  dominantBaseline: "central",
  fill: (theme.vars || theme).palette.text.secondary,
  variants: [
    {
      props: {
        variant: "primary",
      },
      style: {
        fontSize: theme.typography.h5.fontSize,
      },
    },
    {
      props: ({ variant }) => variant !== "primary",
      style: {
        fontSize: theme.typography.body2.fontSize,
      },
    },
    {
      props: {
        variant: "primary",
      },
      style: {
        fontWeight: theme.typography.h5.fontWeight,
      },
    },
    {
      props: ({ variant }) => variant !== "primary",
      style: {
        fontWeight: theme.typography.body2.fontWeight,
      },
    },
  ],
}));

function PieCenterLabel({ primaryText, secondaryText }) {
  const { width, height, left, top } = useDrawingArea();
  const primaryY = top + height / 2 - 10;
  const secondaryY = primaryY + 24;

  return (
    <React.Fragment>
      <StyledText variant="primary" x={left + width / 2} y={primaryY}>
        {primaryText}
      </StyledText>
      <StyledText variant="secondary" x={left + width / 2} y={secondaryY}>
        {secondaryText}
      </StyledText>
    </React.Fragment>
  );
}

PieCenterLabel.propTypes = {
  primaryText: PropTypes.string.isRequired,
  secondaryText: PropTypes.string.isRequired,
};

const colors = [
  "hsl(220, 20%, 65%)",
  "hsl(220, 20%, 42%)",
  "hsl(220, 20%, 35%)",
  "hsl(220, 20%, 25%)",
];

// Function to get flag component based on country code
const getCountryFlag = (countryCode) => {
  switch (countryCode?.toUpperCase()) {
    case "IN":
      return <IndiaFlag />;
    case "US":
      return <UsaFlag />;
    case "BR":
      return <BrazilFlag />;
    default:
      return <GlobeFlag />;
  }
};

// Function to get country name from country code
const getCountryName = (countryCode) => {
  switch (countryCode?.toUpperCase()) {
    case "IN":
      return "India";
    case "US":
      return "USA";
    case "BR":
      return "Brazil";
    case "CA":
      return "Canada";
    default:
      return "Other";
  }
};

export default function ChartUserByCountry({ locationData = [] }) {

  const theme = useTheme();
  // Process location data to get country statistics
  const processCountryData = (data) => {
    const countryCounts = {};

    data.forEach((location) => {
      const country = location.country || "Unknown";
      countryCounts[country] = (countryCounts[country] || 0) + 1;
    });

    const total = Object.values(countryCounts).reduce(
      (sum, count) => sum + count,
      0
    );

    // Convert to chart data format
    const chartData = Object.entries(countryCounts).map(([country, count]) => ({
      label: getCountryName(country),
      value: count,
      countryCode: country,
    }));

    // Sort by count descending and take top 4, group others
    const sortedData = chartData.sort((a, b) => b.value - a.value);
    const topCountries = sortedData.slice(0, 3);
    const otherCountries = sortedData.slice(3);

    const finalData = [...topCountries];
    if (otherCountries.length > 0) {
      const otherCount = otherCountries.reduce(
        (sum, item) => sum + item.value,
        0
      );
      finalData.push({
        label: "Other",
        value: otherCount,
        countryCode: "OTHER",
      });
    }

    return { chartData: finalData, total };
  };

  const { chartData, total } = processCountryData(locationData);

  // Create countries array for the progress bars
  const countries = chartData.map((item, index) => ({
    name: item.label,
    value: total > 0 ? Math.round((item.value / total) * 100) : 0,
    flag: getCountryFlag(item.countryCode),
    color: colors[index] || colors[colors.length - 1],
  }));

  // console.log('countries data--------------->',countries)

  return (
    <Card
      variant="outlined"
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: "8px",
        flexGrow: 1,
        // backgroundColor: theme.palette.mode === "dark" ? "#0f131b !important" : "#f2f7fa",
      }}
    >
      <CardContent>
        <Typography component="h2" variant="subtitle2">
          Users by country
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <PieChart
            colors={colors}
            margin={{
              left: 80,
              right: 80,
              top: 80,
              bottom: 80,
            }}
            series={[
              {
                data: chartData,
                innerRadius: 75,
                outerRadius: 100,
                paddingAngle: 0,
                highlightScope: { faded: "global", highlighted: "item" },
              },
            ]}
            height={260}
            width={260}
            slotProps={{
              legend: { hidden: true },
            }}
          >
            <PieCenterLabel
              primaryText={total.toString()}
              secondaryText="Total"
            />
          </PieChart>
        </Box>
        {countries.map((country, index) => (
          <Stack
            key={index}
            direction="row"
            sx={{ alignItems: "center", gap: 2, pb: 2 }}
          >
            {/* {country.flag} */}
            {/* <img 
                src={`https://flagcdn.com/${country.flag}.svg`}
                alt={`${country.name} flag`}
                style={{ width: 24, height: 16, marginLeft: 4, marginRight: 4 }}
            /> */}
            <Stack sx={{ gap: 1, flexGrow: 1 }}>
              <Stack
                direction="row"
                sx={{
                  justifyContent: "space-between",
                  alignItems: "center",
                  gap: 2,
                }}
              >
                <Typography variant="body2" sx={{ fontWeight: "500" }}>
                  {country.name}
                </Typography>
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  {country.value}%
                </Typography>
              </Stack>
              <LinearProgress
                variant="determinate"
                aria-label="Number of users by country"
                value={country.value}
                sx={{
                  [`& .${linearProgressClasses.bar}`]: {
                    backgroundColor: country.color,
                  },
                }}
              />
            </Stack>
          </Stack>
        ))}
      </CardContent>
    </Card>
  );
}

ChartUserByCountry.propTypes = {
  locationData: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      country: PropTypes.string,
      name: PropTypes.string,
    })
  ),
};
