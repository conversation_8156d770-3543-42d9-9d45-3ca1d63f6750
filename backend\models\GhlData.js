const mysql = require('mysql2/promise');

const createGhlDataTable = async (connection) => {
  try {
    // Create the table if it doesn't exist
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS ghl_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        userId VARCHAR(255) NOT NULL,
        accessToken TEXT NOT NULL,
        refreshToken TEXT NOT NULL,
        expiresIn INT NOT NULL,
        tokenType VARCHAR(50) NOT NULL,
        locationId VARCHAR(255),
        companyId VARCHAR(255),
        scope JSON,
        userType VARCHAR(50),
        uniqueId VARCHAR(255),
        rawResponse JSON,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('GhlData table created successfully');
  } catch (error) {
    console.error('Error creating GhlData table:', error);
    throw error;
  }
};

module.exports = {
  createGhlDataTable
}; 