const express = require("express");
const cors = require("cors");
const bodyParser = require("body-parser");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const router = express.Router();

const app = express();
const JWT_SECRET = "your-secret-key"; // In production, use environment variables

// Middleware
app.use(cors());
app.use(bodyParser.json());
const {
  initUserTable,
  findUserByEmail,
  createUser,
  saveTokens,
} = require("../models/UserModel");

const {
  REFRESH_SECRET,
  generateAccessToken,
  generateRefreshToken,
} = require("../utils/token");

router.post("/register", async (req, res) => {
  const { name, email, password } = req.body;

  if (!name || !email || !password) {
    return res.status(400).json({ message: "All fields are required" });
  }

  try {
    await initUserTable();

    const existingUser = await findUserByEmail(email);
    if (existingUser) {
      return res.status(400).json({ message: "User already exists" });
    }

    const hashedPassword = bcrypt.hashSync(password, 10);
    const id = Date.now().toString();

    await createUser(id, name, email, hashedPassword);

    // Get GHL access token (updated with users.write scope)
    const ghlAccessToken = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bDOVKhGGsMyoCyJWPbHj01VuIT6yGydgsmoeXQPsSi12UMWQbCJ3KCDGorRKQyihHzTJRNq01uOpA4bZ28XhRT94eUNhdzXNtPvvU-nspzy9NTDZSmsHz_YAbulvY0hqpzl5QFLKht0WzX19x1QrlPX763V_PHrZRuA-q8D-R208ybkV4TYEkbUHE-bYtScRxJA9aC6AJ9a7ryjof20xO8tz7sjNyLSP6UJKUrW5Zg-ujzm2LcCfDq9qjDPHkqTiO16YlwFkrjTQ9RIEGdJU8zmttY3_DqZXlIPMeW2iQYoDTNQGzvXJG9Y1Y680GuRNUvIAA0CybfVMEq29DH4eewspudxe9SwaAeZGFaayajwq-mfcBDN8vwZYgUUV97YN2tJt0E8eYfmHlaHPABCpgoieHMb_RTquw1I83dszsT-jFz__CbZTaQTOyB8K1wJsziAut_R8jYt1HQfrMcQsThJoUDwVhz2Y5Jv3HOU0FexNRQyEBE5tFlWi4V2xS2uLzRGmqoPZsty-o0Zl2KWOHt0IzQqzys66l4daeCBZ8E0aw0MrnABFm8fu2TPu_ues5vgnFj6p3JkA2WHT71cr7XcEfm6fK9m1oTCrUKvCoLXDL6_9FzvICvFJ-VfTe2JNZgEOeNN0LwkyM5w-k0uxKa7mOkV83kE58-BK7xkV3vM';
    
    const [firstName, ...rest] = name.trim().split(' ');
    const lastName = rest.join(' ') || '';

    // Create user in HighLevel (now we have users.write scope!)
    try {
      const ghlResponse = await fetch('https://services.leadconnectorhq.com/users/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${ghlAccessToken}`,
          'Version': '2021-07-28',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          companyId: '09ntIsRbx4gAxchbHqe7',
          firstName,
          lastName,
          email,
          password,
          phone: '+***********',
          type: 'account',
          role: 'admin',
          locationIds: ['t5r1YBZkxDCWUO2FyDkN'],
          permissions: {
            campaignsEnabled: true,
            campaignsReadOnly: false,
            contactsEnabled: true,
            workflowsEnabled: true,
            workflowsReadOnly: false,
            triggersEnabled: true,
            funnelsEnabled: true,
            websitesEnabled: false,
            opportunitiesEnabled: true,
            dashboardStatsEnabled: true,
            bulkRequestsEnabled: true,
            appointmentsEnabled: true,
            reviewsEnabled: true,
            onlineListingsEnabled: true,
            phoneCallEnabled: true,
            conversationsEnabled: true,
            assignedDataOnly: false,
            adwordsReportingEnabled: false,
            membershipEnabled: false,
            facebookAdsReportingEnabled: false,
            attributionsReportingEnabled: false,
            settingsEnabled: true,
            tagsEnabled: true,
            leadValueEnabled: true,
            marketingEnabled: true,
            agentReportingEnabled: true,
            botService: false,
            socialPlanner: true,
            bloggingEnabled: true,
            invoiceEnabled: true,
            affiliateManagerEnabled: true,
            contentAiEnabled: true,
            refundsEnabled: true,
            recordPaymentEnabled: true,
            cancelSubscriptionEnabled: true,
            paymentsEnabled: true,
            communitiesEnabled: true,
            exportPaymentsEnabled: true
          }
        }),
      });

      const ghlData = await ghlResponse.json();

      if (!ghlResponse.ok) {
        console.error('GHL API Error:', ghlData);
        // Continue with local registration even if GHL fails
        console.log('Continuing with local registration despite GHL error');
      } else {
        console.log('GHL User created successfully:', ghlData);
      }

    } catch (err) {
      console.error('Error calling GHL API:', err);
      // Continue with local registration even if GHL fails
      console.log('Continuing with local registration despite GHL error');
    }

    const user = { id, email };
    const accessToken = generateAccessToken(user);
    const refreshToken = generateRefreshToken(user);

    await saveTokens(email, accessToken, refreshToken);

    res.status(201).json({
      message: "User registered successfully",
      accessToken,
      refreshToken,
    });
  } catch (err) {
    console.error("Register error:", err.message);
    res.status(500).json({ message: "Internal server error" });
  }
});

// router.post("/refresh-token", async (req, res) => {
//   const { refreshToken } = req.body;

//   if (!refreshToken) {
//     return res.status(400).json({ message: "Refresh token required" });
//   }

//   try {
//     const payload = jwt.verify(refreshToken, REFRESH_SECRET);
//     const user = await findUserByEmail(payload.email);

//     if (!user || user.refreshToken !== refreshToken) {
//       return res.status(403).json({ message: "Invalid refresh token" });
//     }

//     const newAccessToken = generateAccessToken(user);
//     const newRefreshToken = generateRefreshToken(user);

//     await saveTokens(user.email, newAccessToken, newRefreshToken);

//     res.json({
//       accessToken: newAccessToken,
//       refreshToken: newRefreshToken,
//     });
//   } catch (err) {
//     console.error("Refresh error:", err.message);
//     return res.status(403).json({ message: "Token expired or invalid" });
//   }
// });




module.exports = router;