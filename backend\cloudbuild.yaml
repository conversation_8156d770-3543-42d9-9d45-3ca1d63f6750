# steps:
#   - name: 'gcr.io/cloud-builders/npm'
#     args: ['install']

#   - name: 'gcr.io/cloud-builders/npm'
#     args: ['run', 'build']  # optional if build step needed

#   - name: 'gcr.io/cloud-builders/gcloud'
#     args: ['app', 'deploy', '--quiet']
#     dir: '.'  # deploy from root of backend folder

# timeout: 900s

steps:
  - name: 'gcr.io/cloud-builders/npm'
    dir: 'backend'
    args: ['install']

  - name: 'gcr.io/cloud-builders/gcloud'
    dir: 'backend'
    args: ['app', 'deploy', '--quiet']

options:
  logging: CLOUD_LOGGING_ONLY
