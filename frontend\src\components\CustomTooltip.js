// CustomTooltip.js
import React from 'react';
import { styled } from '@mui/material/styles';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';

// Create a colorful tooltip component
const ColorTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme, color = '#17a2b8' }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: color,
    color: '#ffffff',
    fontSize: '14px',
    fontWeight: 500,
    padding: '10px 16px',
    borderRadius: '8px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: color,
  },
}));

export default ColorTooltip;