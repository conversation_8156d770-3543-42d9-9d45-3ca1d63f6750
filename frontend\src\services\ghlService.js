// import { api } from './api';
// import axios from "axios";
// const API_BASE_URL = "http://localhost:5000";
// export const ghlService = {
//   getLocations: async (token) => {
//     const response = await fetch('https://rest.gohighlevel.com/v1/locations/', {
//       headers: {
//         'Authorization': `Bearer ${token}`,
//         'Content-Type': 'application/json'
//       }
//     });
//     return response.json();
//   },
// };


// export const getTransactions = async () => {
//   try {
//     const response = await axios.get(`${API_BASE_URL}/ghl/transactions`);
//     return response.data;
//   } catch (error) {
//     console.error("Error fetching transactions:", error);
//     throw error;
//   }
// };

// src/services/ghlService.js
import axios from 'axios';

const API_URL = process.env.REACT_APP_API_BASE_URL
const NEW_API_URL = process.env.REACT_APP_API_BASE_URL

// console.log(NEW_API_URL)  
// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
    
  }
});

// Service functions
export const getContacts = async () => {
  try {
    const response = await axios.post('/contacts');
    return response.data;
  } catch (error) {
    console.error('Error fetching contacts:', error);
    throw error;
  }
};

// Updated to use pipeline data instead of direct opportunities
export const getPipelineData = async (id) => {
  try {
    const response = await api.get(`/pipeline-data?locationId=${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching pipeline data:', error);
    throw error;
  }
};

// Fallback method (if direct opportunities endpoint starts working)
export const getOpportunities = async (locationId = 't5r1YBZkxDCWUO2FyDkN') => {
  try {
    const response = await api.get(`/opportunities?locationId=${locationId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching opportunities:', error);
    throw error;
  }
};