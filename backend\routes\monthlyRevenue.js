// const express = require("express");
// const axios = require("axios");
// const cors = require("cors");
// require("dotenv").config();

// const router = express.Router();

// const app = express();
// app.use(express.json());
// app.use(cors());

// const GHL_API_KEY = `jg3emkifSwiaWF0IjoxNzQxMzQyNTkwLjczOSwiZXhwIjoxNzQxNDI4OTkwLjczOX0.y51A6sPtveGlFUHlv0B0ZiN8WZrlo8qvoQmaTQaCv-LpiDhFXWSeVSYaFc0iQgB9LMf2yknyzeBmemDHb3nAlBCl-cs4JMdxHFwnUFjg4mP-OSJ3-qSNXVPWe-MmpgqQt0Flylhq_1UlIVdwc0K81UHHuJ84gR34VirOTLXvPpgZyPPoDupqxH2v9Am-tMIR-3w_zfF6h_20Q_STXeoHZqwBcHTQyABXIsMTaaSk_MRgmqoXKj2Dw588woE6V_yUBIkhFLKciUovGXmbfhgJRwiO_ZVlghHymKaeIRrjiMIzURIQ7-VT2vNs0sQwvUA4Mvm2C3Z2s0MyAfHLHTfILNDh4OPgIM500ECLFNZ5PHKsQ3nKVdbmVgFDN4QNiCJ2ByyQ4q1angEkk8qFhwsXJhSJ478GI9PKTQueJy_8DOAxApocTK6eejqerQVaYtWDBOynYfSemr4oPy9PxyRlox_TEso8CM_G-o8GITLVC0gNRh_iFI-n_I5h3Rwm4vgnQ4uWUoiI0VUy1UH8xwKqyibYLq1mEQ_w80pCMGX-pPA6M35o5qkEcYIKaHV5PWeo3oun-1J_Hf9K0kPys9LTfecZmMRjpa_tGVxCF-GAASi4cbYlQKEdRoHosDJuReSZPH7L_t4-d6vMa6DyOs2UCCkXjcL7_zdwvz4b-XmmLhAeyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdXRoQ2xhc3MiOiJMb2NhdGlvbiIsImF1dGhDbGFzc0lkIjoiSlh4OUE1TWQ2MjN4cXFIdG5DUUkiLCJzb3VyY2UiOiJJTlRFR1JBVElPTiIsInNvdXJjZUlkIjoiNjc5OTBjOTU1ZWVmZGYyZmY1NDkzY2ZkLW03MGI4N3ppIiwiY2hhbm5lbCI6Ik9BVVRIIiwicHJpbWFyeUF1dGhDbGFzc0lkIjoiSlh4OUE1TWQ2MjN4cXFIdG5DUUkiLCJvYXV0aE1ldGEiOnsic2NvcGVzIjpbImJ1c2luZXNzZXMucmVhZG9ubHkiLCJidXNpbmVzc2VzLndyaXRlIiwibG9jYXRpb25zLndyaXRlIiwibG9jYXRpb25zLnJlYWRvbmx5IiwiY29udGFjdHMucmVhZG9ubHkiLCJjb250YWN0cy53cml0ZSIsImNvbXBhbmllcy5yZWFkb25seSJdLCJjbGllbnQiOiI2Nzk5MGM5NTVlZWZkZjJmZjU0OTNjZmQiLCJjbGllbnRLZXkiOiI2Nzk5MGM5NTVlZWZkZjJmZjU0OTNjZmQtbTcwYjg3emkifSwiaWF0IjoxNzQxMzQyNTkwLjczOSwiZXhwIjoxNzQxNDI4OTkwLjczOX0.y51A6sPtveGlFUHlv0B0ZiN8WZrlo8qvoQmaTQaCv-LpiDhFXWSeVSYaFc0iQgB9LMf2yknyzeBmemDHb3nAlBCl-cs4JMdxHFwnUFjg4mP-OSJ3-qSNXVPWe-MmpgqQt0Flylhq_1UlIVdwc0K81UHHuJ84gR34VirOTLXvPpgZyPPoDupqxH2v9Am-tMIR-3w_zfF6h_20Q_STXeoHZqwBcHTQyABXIsMTaaSk_MRgmqoXKj2Dw588woE6V_yUBIkhFLKciUovGXmbfhgJRwiO_ZVlghHymKaeIRrjiMIzURIQ7-VT2vNs0sQwvUA4Mvm2C3Z2s0MyAfHLHTfILNDh4OPgIM500ECLFNZ5PHKsQ3nKVdbmVgFDN4QNiCJ2ByyQ4q1angEkk8qFhwsXJhSJ478GI9PKTQueJy_8DOAxApocTK6eejqerQVaYtWDBOynYfSemr4oPy9PxyRlox_TEso8CM_G-o8GITLVC0gNRh_iFI-n_I5h3Rwm4vgnQ4uWUoiI0VUy1UH8xwKqyibYLq1mEQ_w80pCMGX-pPA6M35o5qkEcYIKaHV5PWeo3oun-1J_Hf9K0kPys9LTfecZmMRjpa_tGVxCF-GAASi4cbYlQKEdRoHosDJuReSZPH7L_t4-d6vMa6DyOs2UCCkXjcL7_zdwvz4b-XmmLhA`;
// const GHL_LOCATION_ID = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6InQ1cjFZQlpreERDV1VPMkZ5RGtOIiwiY29tcGFueV9pZCI6IjA5bnRJc1JieDRnQXhjaGJIcWU3IiwidmVyc2lvbiI6MSwiaWF0IjoxNjg1NzI3MjA5NjYxLCJzdWIiOiJ1c2VyX2lkIn0.-UcbUyP-4nxElkvgSfm8s_78bk0cWb3A44B_vZj-29s"; // Replace with actual location ID

// // Get monthly revenue
// router.get("/payments/transactions", async (req, res) => {
//   try {
//     const response = await axios.get(
//       `https://services.leadconnectorhq.com/payments/transactions`, 
//       {
//        headers: {
//       Authorization: `Bearer ${GHL_API_KEY} `,
//       Accept: "application/json",
//       Version: "2021-07-28", // ✅ Add the required version header
//     },
//       }
//     );

//     console.log("API Response:", response.data); // Log API response

//     if (!response.data.transactions) {
//       return res.status(404).json({ error: "No transactions found" });
//     }

//     const transactions = response.data.transactions;
//     const currentMonth = new Date().getMonth() + 1;
//     const currentYear = new Date().getFullYear();

//     // Filter transactions for the current month
//     const monthlyTransactions = transactions.filter((transaction) => {
//       if (!transaction.createdAt) return false; // Ignore transactions without dates
//       const transactionDate = new Date(transaction.createdAt);
//       return (
//         transactionDate.getMonth() + 1 === currentMonth &&
//         transactionDate.getFullYear() === currentYear
//       );
//     });
    
//     // Calculate total revenue
//     const totalRevenue = monthlyTransactions.reduce((sum, transaction) => {
//       return sum + (transaction.amount || 0); // Ensure no null values
//     }, 0);

//     res.json({ month: currentMonth, year: currentYear, totalRevenue });
//   } catch (error) {
//     console.error("Error fetching revenue data:", error.response ? error.response.data : error.message);
//     res.status(500).json({ 
//       error: "Failed to fetch revenue data", 
//       details: error.response ? error.response.data : error.message 
//     });
//   }
// });

// module.exports = router;

const express = require("express");
const moment = require("moment");
const router = express.Router();

// Your GHL API key
const GHL_API_KEY = "YOUR_API_KEY";
const GHL_BASE_URL = "https://rest.gohighlevel.com";

async function fetchFromGHL(endpoint, params = {}) {
  const url = new URL(`${GHL_BASE_URL}${endpoint}`);
  Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: { Authorization: `Bearer ${GHL_API_KEY}` },
    });
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error(`Error fetching ${endpoint}:`, error.message);
    return null;
  }
}

async function getOpportunities(startDate, endDate) {
  const data = await fetchFromGHL("/v1/pipelines/opportunities/", { startDate, endDate, limit: 100 });
  return data?.opportunities || [];
}

async function getPayments(startDate, endDate) {
  const data = await fetchFromGHL("/v1/contacts/payments/", { startDate, endDate, limit: 100 });
  return data?.payments || [];
}

router.get("/test", async (req, res) => {
  const data = await fetchFromGHL("/v1/custom-fields/");
  if (data) {
    res.json({ status: "success", data });
  } else {
    res.status(500).json({ status: "error", message: "API connection failed" });
  }
});

router.get("/revenue", async (req, res) => {
  try {
    const months = parseInt(req.query.months) || 12;
    const monthlyRevenue = {};
    const currentDate = moment();

    for (let i = 0; i < months; i++) {
      const startDate = moment(currentDate).subtract(i, "months").startOf("month").format("YYYY-MM-DD");
      const endDate = moment(currentDate).subtract(i, "months").endOf("month").format("YYYY-MM-DD");
      const monthKey = moment(startDate).format("YYYY-MM");

      const opportunities = await getOpportunities(startDate, endDate);
      const payments = await getPayments(startDate, endDate);
      
      let opportunityRevenue = opportunities.reduce((sum, opp) => sum + (opp.status === "won" || opp.status === "closed" ? parseFloat(opp.monetaryValue || 0) : 0), 0);
      let paymentsRevenue = payments.reduce((sum, payment) => sum + parseFloat(payment.amount || 0), 0);
      
      monthlyRevenue[monthKey] = {
        month: moment(startDate).format("MMMM YYYY"),
        opportunityRevenue,
        paymentsRevenue,
        totalRevenue: opportunityRevenue + paymentsRevenue,
      };
    }

    res.json(Object.values(monthlyRevenue));
  } catch (error) {
    res.status(500).json({ error: "Failed to calculate revenue", message: error.message });
  }
});

module.exports = router;

