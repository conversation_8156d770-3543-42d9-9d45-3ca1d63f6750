import React, { useEffect, useState } from "react";
import {
  Box,
  Button,
  Checkbox,
  Divider,
  IconButton,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Stack,
  Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import FilterAltOutlinedIcon from "@mui/icons-material/FilterAltOutlined";
import {
  mockFields,
  menuOptions,
  sortOptions,
  allFieldValues,
} from "../contant/mockData";

export default function ManageContactDrawer({ onClose }) {
  const [filter, setFilterOpen] = React.useState(false);
  const [activeFilterMenu, setActiveFilterMenu] = React.useState(null); // null = show fields list
  const [selectedFilterOptions, setSelectedFilterOptions] = React.useState({});

  const toggleFilterDrawer = (newOpen) => () => setFilterOpen(newOpen);

  const handleFieldClick = (fieldId) => setActiveFilterMenu(fieldId);

  const handleBack = () => setActiveFilterMenu(null);

  const handleFilterCheckboxChange = (fieldId, optionId) => {
    setSelectedFilterOptions((prev) => {
      const current = prev[fieldId] || [];
      const updated = current.includes(optionId)
        ? current.filter((id) => id !== optionId)
        : [...current, optionId];
      return { ...prev, [fieldId]: updated };
    });
  };
  return (
    <Box
      role="presentation"
      display="flex"
      flexDirection="column"
      height="100%"
    >
      <Box px={3} py={2} flexShrink={0}>
        <Stack direction="row" justifyContent="space-between">
          <Box>
            <Typography variant="h4">Filters</Typography>
            <Typography variant="body2" color="text.disabled">
              Apply filters to contacts
            </Typography>
          </Box>
          <Box>
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Stack>
      </Box>

      <Divider />
      <Box flexGrow={1} overflow="auto" sx={{ px: 2, pt: 2 }}>
        {!activeFilterMenu && (
          <List>
            {mockFields.map((field) => (
              <ListItemButton
                key={field.id}
                sx={{
                  mb: "15px",
                  display: "flex",
                  justifyContent: "space-between",
                  borderRadius: "8px",
                  // backgroundColor: "#f2f4f7",
                  backgroundColor: (theme) =>
                    theme.palette.mode === "dark"
                      ? theme.palette.grey[800]
                      : "#f2f7fa",
                }}
                onClick={() => handleFieldClick(field.id)}
              >
                <ListItemText primary={field.label} />
                <ListItemIcon>
                  <ChevronRightIcon />
                </ListItemIcon>
              </ListItemButton>
            ))}
          </List>
        )}

        {activeFilterMenu && (
          <Box>
            <Button
              startIcon={<ArrowBackIosIcon />}
              onClick={handleBack}
              sx={{ mb: 1, textTransform: "none" }}
            >
              Back
            </Button>

            <Typography variant="h6" mb={2}>
              {mockFields.find((f) => f.id === activeFilterMenu)?.label}
            </Typography>

            <List>
              {(menuOptions[activeFilterMenu] || []).map((option) => (
                <ListItemButton
                  key={option.id}
                  onClick={() =>
                    handleFilterCheckboxChange(activeFilterMenu, option.id)
                  }
                  sx={{
                    mb: "15px",
                    borderRadius: "8px",
                    py: 1,
                    backgroundColor: (theme) =>
                      theme.palette.mode === "dark"
                        ? theme.palette.grey[800]
                        : "#f2f7fa",
                  }}
                >
                  <Checkbox
                    edge="start"
                    checked={(
                      selectedFilterOptions[activeFilterMenu] || []
                    ).includes(option.id)}
                    sx={{
                      my: .5,
                    }}
                  />
                  <ListItemText primary={option.label} />
                </ListItemButton>
              ))}
            </List>
          </Box>
        )}
      </Box>

      <Box px={3} py={2} flexShrink={0}>
        <Stack direction="row" gap={1} justifyContent="end">
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="contained">Apply</Button>
        </Stack>
      </Box>
    </Box>
  );
}
