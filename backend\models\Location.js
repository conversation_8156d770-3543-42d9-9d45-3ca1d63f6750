const mongoose = require('mongoose');

const locationSchema = new mongoose.Schema({
  locationId: {
    type: String,
    required: true,
    unique: true
  },
  business: {
    name: {
      type: String,
      required: true
    },
    address: {
      type: String,
      default: ''
    },
    city: {
      type: String,
      default: ''
    },
    state: {
      type: String,
      default: ''
    },
    zip: {
      type: String,
      default: ''
    },
    country: {
      type: String,
      default: ''
    }
  },
  companyId: {
    type: String,
    required: true
  },
  userId: {
    type: String,
    required: true
  },
  // Store the original GHL data for reference
  ghlData: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true // This adds createdAt and updatedAt fields
});

// Index for efficient querying
locationSchema.index({ companyId: 1, createdAt: -1 });
locationSchema.index({ locationId: 1 });

module.exports = mongoose.model('Location', locationSchema); 