// LeadsBySourceBarChart.js
import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  Typography,
  Box,
  useTheme,
  CircularProgress,
  Al<PERSON>,
  But<PERSON>,
} from "@mui/material";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
  ReferenceArea,
} from "recharts";
import { useParams } from "react-router-dom";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { subDays } from "date-fns";
import ColorTooltip from "../../components/CustomTooltip";

export default function LeadsBySourceBarChart() {
  const theme = useTheme();
  const { locationId } = useParams();
  const today = new Date();
  const [startDate, setStartDate] = useState(subDays(today, 7));
  const [endDate, setEndDate] = useState(today);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [chartData, setChartData] = useState([]);
  const [totalLeads, setTotalLeads] = useState(0);
  const [hoveredBarIndex, setHoveredBarIndex] = useState(null); // 👈 New state

  const COLORS = [
    "#2196F3",
    "#4CAF50",
    "#FF9800",
    "#9C27B0",
    "#F44336",
    "#00BCD4",
    "#795548",
    "#607D8B",
    "#E91E63",
    "#3F51B5",
  ];

  const handleStartDateChange = (newDate) => {
    setStartDate(newDate);
    if (newDate && endDate && newDate > endDate) {
      setEndDate(newDate);
    }
  };

  const handleEndDateChange = (newDate) => {
    setEndDate(newDate);
  };

  const formatSourceName = (source) => {
    const nameMap = {
      facebook: "Facebook",
      website: "Website",
      "google my business": "Google My Business",
      "facebook form lead": "Facebook Ads",
      booking_widget: "Booking Widget",
      unknown: "Unknown",
      "detroit.gonano.com - get quote": "Detroit Quote",
      "detroit.gonano.com - shingles": "Detroit Shingles",
      "client free evaluation": "Free Evaluation",
      "find a dealer": "Find a Dealer",
      "guaranteed estimates": "Guaranteed Estimates",
      "gonano.com lead": "Gonano Lead",
      "gonano corp lead": "Gonano Corp",
      "albany website": "Albany Website",
    };

    return (
      nameMap[source.toLowerCase()] ||
      source
        .split(/[-_]/)
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    );
  };

  const fetchLeadsData = async () => {
    if (!startDate || !endDate || startDate > endDate) return;

    setLoading(true);
    setError(null);

    const authToken = localStorage.getItem("locationAccessTokenContact");
    if (!authToken) {
      setError("No authorization token found");
      setLoading(false);
      return;
    }
    if (!locationId) {
      setError("No location ID found in URL");
      setLoading(false);
      return;
    }

    const dateRange = {
      gte: new Date(startDate.setHours(0, 0, 0, 0)).toISOString(),
      lte: new Date(endDate.setHours(23, 59, 59, 999)).toISOString(),
    };

    try {
      const response = await fetch("http://localhost:5000/api/leadGenrateV2", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          locationId,
          pageLimit: 500,
          page: 1,
          filters: [
            {
              field: "dateAdded",
              operator: "range",
              value: dateRange,
            },
          ],
          sort: [{ field: "dateAdded", direction: "desc" }],
        }),
      });

      if (!response.ok) throw new Error(`API error: ${response.status}`);
      const result = await response.json();

      if (result.sources) {
        const sourcesArray = Object.entries(result.sources)
          .map(([name, value]) => ({
            name: formatSourceName(name),
            value: parseInt(value),
            originalName: name,
          }))
          .sort((a, b) => b.value - a.value)
          .slice(0, 10);

        setChartData(sourcesArray);
        const total = Object.values(result.sources).reduce(
          (sum, val) => sum + parseInt(val),
          0
        );
        setTotalLeads(total);
      }

      setLoading(false);
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };

  useEffect(() => {
    if (startDate && endDate && locationId) {
      fetchLeadsData();
    }
  }, [locationId]);

  const liveTime = new Date();
  const tomorrow = new Date(
    liveTime.getFullYear(),
    liveTime.getMonth(),
    liveTime.getDate() + 1
  );

  const handleSubmit = () => {
    const isInvalidDate =
      !startDate ||
      !endDate ||
      startDate > endDate ||
      startDate >= tomorrow ||
      endDate >= tomorrow;

    if (isInvalidDate) return;

    fetchLeadsData();
  };

  const CustomChartTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const value = payload[0].value;
      const dataIndex = chartData.findIndex((item) => item.name === label);
      const barColor = COLORS[dataIndex % COLORS.length];

      return (
        <Box
          sx={{
            backgroundColor: barColor,
            color: "#ffffff",
            p: 2,
            borderRadius: "8px",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
          }}
        >
          <Typography variant="body2" fontWeight="bold">
            {label}
          </Typography>
          <Typography variant="body2">
            Leads: {value.toLocaleString()}
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.9 }}>
            {((value / totalLeads) * 100).toFixed(1)}% of total
          </Typography>
        </Box>
      );
    }
    return null;
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Card
        sx={{
          backgroundColor: (theme) =>
            theme.palette.mode === "dark" ? theme.palette.grey[900] : "#ffffff",
          boxShadow:
            theme.palette.mode === "dark"
              ? "0 1px 3px rgba(0,0,0,0.5)"
              : "0 1px 3px rgba(0,0,0,0.12)",
          borderRadius: 2,
        }}
      >
        <CardContent>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" fontWeight="bold">
              Leads
            </Typography>
            <Typography variant="h6" fontWeight='bold' color="text.secondary" sx={{ mb: 2 }}>
              Total leads: {totalLeads.toLocaleString()}
            </Typography>

            <Box
              sx={{
                display: "flex",
                gap: 2,
                flexWrap: "wrap",
                alignItems: "flex-start",
              }}
            >
              <DatePicker
                label="From Date"
                value={startDate}
                onChange={handleStartDateChange}
                maxDate={today}
                disabled={loading}
                slotProps={{
                  textField: {
                    sx: { minWidth: 180, flex: 1 },
                    helperText: "Select start date",
                  },
                }}
              />

              <DatePicker
                label="To Date"
                value={endDate}
                onChange={handleEndDateChange}
                minDate={startDate}
                maxDate={today}
                disabled={loading}
                slotProps={{
                  textField: {
                    sx: { minWidth: 180, flex: 1 },
                    helperText: "Must be after start date",
                  },
                }}
              />

              <Button
                variant={
                  loading ||
                  !startDate ||
                  !endDate ||
                  startDate > endDate ||
                  startDate >= tomorrow ||
                  endDate >= tomorrow
                    ? "outlined"
                    : "contained"
                }
                onClick={handleSubmit}
                disabled={
                  loading ||
                  !startDate ||
                  !endDate ||
                  startDate > endDate ||
                  startDate >= tomorrow ||
                  endDate >= tomorrow
                }
                sx={{
                  minWidth: 100,
                  height: 56,
                  alignSelf: "flex-start",
                  cursor:
                    loading ||
                    !startDate ||
                    !endDate ||
                    startDate > endDate ||
                    startDate >= tomorrow ||
                    endDate >= tomorrow
                      ? "not-allowed"
                      : "pointer",
                }}
              >
                Submit
              </Button>
            </Box>
          </Box>

          {loading && (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: 400,
              }}
            >
              <CircularProgress />
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {!loading && !error && chartData.length > 0 && (
            <Box sx={{ backgroundColor: "#f2f7fa", borderRadius: 1, p: 1 }}>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart
                  data={chartData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                  style={{ borderRadius: 8 }}
                  fill="#e3f2fd"
                  fillOpacity={0.7}
                >
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke={theme.palette.divider}
                  />
                  <XAxis
                    dataKey="name"
                    stroke={theme.palette.text.secondary}
                    height={60}
                    interval={0}
                    tick={(props) => {
                      const { x, y, payload } = props;
                      const text = payload.value;
                      const maxLength = 10;
                      const needsTruncation = text.length > maxLength;
                      const displayText = needsTruncation
                        ? text.substring(0, maxLength) + "..."
                        : text;

                      return (
                        <g transform={`translate(${x},${y})`}>
                          <foreignObject x={-50} y={0} width={100} height={40}>
                            <ColorTooltip
                              title={text}
                              color="#17a2b8"
                              arrow
                              placement="bottom"
                            >
                              <div
                                style={{
                                  textAlign: "center",
                                  cursor: needsTruncation ? "help" : "default",
                                  fontSize: "11px",
                                  color: theme.palette.text.secondary,
                                  paddingTop: "16px",
                                }}
                              >
                                {displayText}
                              </div>
                            </ColorTooltip>
                          </foreignObject>
                        </g>
                      );
                    }}
                  />
                  <YAxis
                    stroke={theme.palette.text.secondary}
                    style={{ fontSize: "12px" }}
                  />
                  <Tooltip content={<CustomChartTooltip />} />

                  {/* {hoveredBarIndex !== null && (
                    <ReferenceArea
                      x1={chartData[hoveredBarIndex].name}
                      x2={chartData[hoveredBarIndex].name}
                      strokeOpacity={0}
                      fill="#e3f2fd"
                      fillOpacity={0.6}
                    />
                  )} */}

                  <Bar
                    dataKey="value"
                    radius={[8, 8, 0, 0]}
                    onMouseOver={(_, index) => setHoveredBarIndex(index)}
                    onMouseOut={() => setHoveredBarIndex(null)}
                  >
                    {chartData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </Box>
          )}

          {!loading && !error && chartData.length === 0 && (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="body1" color="text.secondary">
                No leads found for the selected date range
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </LocalizationProvider>
  );
}

// // LeadsBySourceBarChart.js
// import React, { useState, useEffect } from "react";
// import {
//   Card,
//   CardContent,
//   Typography,
//   Box,
//   useTheme,
//   CircularProgress,
//   Alert,
//   Button,
// } from "@mui/material";
// import {
//   BarChart,
//   Bar,
//   XAxis,
//   YAxis,
//   CartesianGrid,
//   Tooltip,
//   ResponsiveContainer,
//   Cell,
// } from "recharts";
// import { useParams } from "react-router-dom";
// import { DatePicker } from "@mui/x-date-pickers/DatePicker";
// import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
// import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
// import { subDays } from "date-fns";
// import ColorTooltip from "../../components/CustomTooltip"; // Import the tooltip component

// export default function LeadsBySourceBarChart() {
//   const theme = useTheme();
//   const { locationId } = useParams();
//   const today = new Date();
//   const [startDate, setStartDate] = useState(subDays(today, 7));
//   const [endDate, setEndDate] = useState(today);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState(null);
//   const [chartData, setChartData] = useState([]);
//   const [totalLeads, setTotalLeads] = useState(0);

//   // Colors for sources
//   const COLORS = [
//     "#2196F3",
//     "#4CAF50",
//     "#FF9800",
//     "#9C27B0",
//     "#F44336",
//     "#00BCD4",
//     "#795548",
//     "#607D8B",
//     "#E91E63",
//     "#3F51B5",
//   ];

//   // Handle date changes
//   const handleStartDateChange = (newDate) => {
//     setStartDate(newDate);
//     if (newDate && endDate && newDate > endDate) {
//       setEndDate(newDate);
//     }
//   };

//   const handleEndDateChange = (newDate) => {
//     setEndDate(newDate);
//   };

//   // Helper function to format source names
//   const formatSourceName = (source) => {
//     const nameMap = {
//       facebook: "Facebook",
//       website: "Website",
//       "google my business": "Google My Business",
//       "facebook form lead": "Facebook Ads",
//       booking_widget: "Booking Widget",
//       unknown: "Unknown",
//       "detroit.gonano.com - get quote": "Detroit Quote",
//       "detroit.gonano.com - shingles": "Detroit Shingles",
//       "client free evaluation": "Free Evaluation",
//       "find a dealer": "Find a Dealer",
//       "guaranteed estimates": "Guaranteed Estimates",
//       "gonano.com lead": "Gonano Lead",
//       "gonano corp lead": "Gonano Corp",
//       "albany website": "Albany Website",
//     };

//     return (
//       nameMap[source.toLowerCase()] ||
//       source
//         .split(/[-_]/)
//         .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
//         .join(" ")
//     );
//   };

//   // Fetch data from API
//   const fetchLeadsData = async () => {
//     if (!startDate || !endDate || startDate > endDate) {
//       return;
//     }

//     setLoading(true);
//     setError(null);

//     const authToken = localStorage.getItem("locationAccessTokenContact");

//     if (!authToken) {
//       setError("No authorization token found");
//       setLoading(false);
//       return;
//     }

//     if (!locationId) {
//       setError("No location ID found in URL");
//       setLoading(false);
//       return;
//     }

//     const dateRange = {
//       gte: new Date(startDate.setHours(0, 0, 0, 0)).toISOString(),
//       lte: new Date(endDate.setHours(23, 59, 59, 999)).toISOString(),
//     };

//     try {
//       const response = await fetch("http://localhost:5000/api/leadGenrateV2", {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//           Authorization: `Bearer ${authToken}`,
//         },
//         body: JSON.stringify({
//           locationId: locationId,
//           pageLimit: 500,
//           page: 1,
//           filters: [
//             {
//               field: "dateAdded",
//               operator: "range",
//               value: dateRange,
//             },
//           ],
//           sort: [
//             {
//               field: "dateAdded",
//               direction: "desc",
//             },
//           ],
//         }),
//       });

//       if (!response.ok) {
//         throw new Error(`API error: ${response.status}`);
//       }

//       const result = await response.json();

//       // Process sources data for bar chart
//       if (result.sources) {
//         const sourcesArray = Object.entries(result.sources)
//           .map(([name, value]) => ({
//             name: formatSourceName(name),
//             value: parseInt(value),
//             originalName: name,
//           }))
//           .sort((a, b) => b.value - a.value)
//           .slice(0, 10); // Show top 10 sources

//         setChartData(sourcesArray);

//         // Calculate total
//         const total = Object.values(result.sources).reduce(
//           (sum, val) => sum + parseInt(val),
//           0
//         );
//         setTotalLeads(total);
//       }

//       setLoading(false);
//     } catch (err) {
//       console.error("Error fetching leads data:", err);
//       setError(err.message);
//       setLoading(false);
//     }
//   };

//   // Initial load
//   useEffect(() => {
//     if (startDate && endDate && locationId) {
//       fetchLeadsData();
//     }
//   }, [locationId]);

//   // Handle submit
//   const handleSubmit = () => {
//     if (startDate && endDate) {
//       fetchLeadsData();
//     }
//   };

//   // Custom colorful tooltip for the chart
//   const CustomChartTooltip = ({ active, payload, label }) => {
//     if (active && payload && payload.length) {
//       const value = payload[0].value;

//       // Find the index of the current bar to get its color
//       const dataIndex = chartData.findIndex((item) => item.name === label);
//       const barColor = COLORS[dataIndex % COLORS.length]; // Use the same color as the bar

//       return (
//         <Box
//           sx={{
//             backgroundColor: barColor, // Use the bar's color
//             color: "#ffffff",
//             p: 2,
//             borderRadius: "8px",
//             boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
//           }}
//         >
//           <Typography
//             variant="body2"
//             fontWeight="bold"
//             sx={{ color: "inherit" }}
//           >
//             {label}
//           </Typography>
//           <Typography variant="body2" sx={{ color: "inherit" }}>
//             Leads: {value.toLocaleString()}
//           </Typography>
//           <Typography variant="caption" sx={{ color: "inherit", opacity: 0.9 }}>
//             {((value / totalLeads) * 100).toFixed(1)}% of total
//           </Typography>
//         </Box>
//       );
//     }
//     return null;
//   };
//   return (
//     <LocalizationProvider dateAdapter={AdapterDateFns}>
//       <Card
//         sx={{
//           backgroundColor: (theme) =>
//             theme.palette.mode === "dark" ? theme.palette.grey[900] : "#ffffff",
//           boxShadow:
//             theme.palette.mode === "dark"
//               ? "0 1px 3px rgba(0,0,0,0.5)"
//               : "0 1px 3px rgba(0,0,0,0.12)",
//           borderRadius: 2,
//         }}
//       >
//         <CardContent>
//           <Box sx={{ mb: 3 }}>
//             <Typography variant="h6" component="h2" fontWeight="bold">
//               Leads by Source
//             </Typography>
//             <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
//               Total leads in selected period: {totalLeads.toLocaleString()}
//             </Typography>

//             <Box
//               sx={{
//                 display: "flex",
//                 gap: 2,
//                 flexWrap: "wrap",
//                 alignItems: "flex-start",
//               }}
//             >
//               <DatePicker
//                 label="From Date"
//                 value={startDate}
//                 onChange={handleStartDateChange}
//                 maxDate={today}
//                 disabled={loading}
//                 slotProps={{
//                   textField: {
//                     sx: { minWidth: 180, flex: 1 },
//                     helperText: "Select start date",
//                   },
//                 }}
//               />

//               <DatePicker
//                 label="To Date"
//                 value={endDate}
//                 onChange={handleEndDateChange}
//                 minDate={startDate}
//                 maxDate={today}
//                 disabled={loading}
//                 slotProps={{
//                   textField: {
//                     sx: { minWidth: 180, flex: 1 },
//                     helperText: "Must be after start date",
//                   },
//                 }}
//               />

//               <Button
//                 variant="contained"
//                 onClick={handleSubmit}
//                 disabled={loading || !startDate || !endDate}
//                 sx={{
//                   minWidth: 100,
//                   height: 56,
//                   alignSelf: "flex-start",
//                 }}
//               >
//                 Submit
//               </Button>
//             </Box>
//           </Box>

//           {loading && (
//             <Box
//               sx={{
//                 display: "flex",
//                 justifyContent: "center",
//                 alignItems: "center",
//                 height: 400,
//               }}
//             >
//               <CircularProgress />
//             </Box>
//           )}

//           {error && (
//             <Alert severity="error" sx={{ mb: 2 }}>
//               {error}
//             </Alert>
//           )}

//           {!loading && !error && chartData.length > 0 && (
//             <ResponsiveContainer width="100%" height={400}>
//               <BarChart
//                 data={chartData}
//                 margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
//                 style={{
//                   backgroundColor: "#ffffff", // 👈 Set your desired background here
//                   borderRadius: "8px",
//                 }}
//               >
//                 <defs>
//                   <rect
//                     id="chartBackground"
//                     width="100%"
//                     height="100%"
//                     fill={theme.palette.mode === "dark" ? "#20242b" : "#f2f7fa"}
//                   />
//                 </defs>
//                 <CartesianGrid strokeDasharray="1 1" stroke="#fff" />
//                 <XAxis
//                   dataKey="name"
//                   stroke={theme.palette.text.secondary}
//                   angle={0}
//                   textAnchor="middle"
//                   height={60}
//                   interval={0}
//                   tick={(props) => {
//                     const { x, y, payload } = props;
//                     const maxLength = 10;
//                     const text = payload.value;
//                     const needsTruncation = text.length > maxLength;
//                     const displayText = needsTruncation
//                       ? text.substring(0, maxLength).trim() + "..."
//                       : text;

//                     return (
//                       <g transform={`translate(${x},${y})`}>
//                         <foreignObject x={-50} y={0} width={100} height={40}>
//                           <ColorTooltip
//                             title={text}
//                             color="#17a2b8"
//                             arrow
//                             placement="bottom"
//                           >
//                             <div
//                               style={{
//                                 textAlign: "center",
//                                 cursor: needsTruncation ? "help" : "default",
//                                 fontSize: "11px",
//                                 color: theme.palette.text.secondary,
//                                 paddingTop: "16px",
//                               }}
//                             >
//                               {displayText}
//                             </div>
//                           </ColorTooltip>
//                         </foreignObject>
//                       </g>
//                     );
//                   }}
//                 />
//                 <YAxis
//                   stroke={theme.palette.text.secondary}
//                   style={{ fontSize: "12px" }}
//                 />
//                 <Tooltip content={<CustomChartTooltip />} />
//                 <Bar dataKey="value" radius={[8, 8, 0, 0]}>
//                   {chartData.map((entry, index) => (
//                     <Cell
//                       key={`cell-${index}`}
//                       fill={COLORS[index % COLORS.length]}
//                     />
//                   ))}
//                 </Bar>
//               </BarChart>
//             </ResponsiveContainer>
//             // <ResponsiveContainer width="100%" height={400}>
//             //   <BarChart
//             //     data={chartData}
//             //     margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
//             //   >
//             //     <CartesianGrid
//             //       strokeDasharray="3 3"
//             //       stroke={theme.palette.divider}
//             //     />
//             //     <XAxis
//             //       dataKey="name"
//             //       stroke={theme.palette.text.secondary}
//             //       angle={0}
//             //       textAnchor="middle"
//             //       height={60}
//             //       interval={0}
//             //       tick={(props) => {
//             //         const { x, y, payload } = props;
//             //         const maxLength = 10;
//             //         const text = payload.value;
//             //         const needsTruncation = text.length > maxLength;
//             //         const displayText = needsTruncation
//             //           ? text.substring(0, maxLength).trim() + "..."
//             //           : text;

//             //         return (
//             //           <g transform={`translate(${x},${y})`}>
//             //             <foreignObject x={-50} y={0} width={100} height={40}>
//             //               <ColorTooltip
//             //                 title={text}
//             //                 color="#17a2b8"
//             //                 arrow
//             //                 placement="bottom"
//             //               >
//             //                 <div
//             //                   style={{
//             //                     textAlign: "center",
//             //                     cursor: needsTruncation ? "help" : "default",
//             //                     fontSize: "11px",
//             //                     color: theme.palette.text.secondary,
//             //                     paddingTop: "16px",
//             //                   }}
//             //                 >
//             //                   {displayText}
//             //                 </div>
//             //               </ColorTooltip>
//             //             </foreignObject>
//             //           </g>
//             //         );
//             //       }}
//             //     />
//             //     <YAxis
//             //       stroke={theme.palette.text.secondary}
//             //       style={{ fontSize: "12px" }}
//             //     />
//             //     <Tooltip content={<CustomChartTooltip />} />
//             //     <Bar dataKey="value" radius={[8, 8, 0, 0]}>
//             //       {chartData.map((entry, index) => (
//             //         <Cell
//             //           key={`cell-${index}`}
//             //           fill={COLORS[index % COLORS.length]}
//             //         />
//             //       ))}
//             //     </Bar>
//             //   </BarChart>
//             // </ResponsiveContainer>
//           )}

//           {!loading && !error && chartData.length === 0 && (
//             <Box sx={{ textAlign: "center", py: 4 }}>
//               <Typography variant="body1" color="text.secondary">
//                 No leads found for the selected date range
//               </Typography>
//             </Box>
//           )}
//         </CardContent>
//       </Card>
//     </LocalizationProvider>
//   );
// }

// import React, { useState, useEffect } from 'react';
// import {
//   Card,
//   CardContent,
//   Typography,
//   Box,
//   useTheme,
//   CircularProgress,
//   Alert,
//   Button,
// } from '@mui/material';
// import {
//   BarChart,
//   Bar,
//   XAxis,
//   YAxis,
//   CartesianGrid,
//   Tooltip,
//   ResponsiveContainer,
//   Cell,
// } from 'recharts';
// import { useParams } from "react-router-dom";
// import { DatePicker } from '@mui/x-date-pickers/DatePicker';
// import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
// import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
// import { subDays } from 'date-fns';

// export default function LeadsBySourceBarChart() {
//   const theme = useTheme();
//   const { locationId } = useParams();
//   const today = new Date();
//   const [startDate, setStartDate] = useState(subDays(today, 7));
//   const [endDate, setEndDate] = useState(today);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState(null);
//   const [chartData, setChartData] = useState([]);
//   const [totalLeads, setTotalLeads] = useState(0);

//   // Colors for sources
//   const COLORS = [
//     '#2196F3',
//     '#4CAF50',
//     '#FF9800',
//     '#9C27B0',
//     '#F44336',
//     '#00BCD4',
//     '#795548',
//     '#607D8B',
//     '#E91E63',
//     '#3F51B5'
//   ];

//   // Handle date changes
//   const handleStartDateChange = (newDate) => {
//     setStartDate(newDate);
//     if (newDate && endDate && newDate > endDate) {
//       setEndDate(newDate);
//     }
//   };

//   const handleEndDateChange = (newDate) => {
//     setEndDate(newDate);
//   };

//   // Helper function to format source names
//   const formatSourceName = (source) => {
//     const nameMap = {
//       'facebook': 'Facebook',
//       'website': 'Website',
//       'google my business': 'Google My Business',
//       'facebook form lead': 'Facebook Ads',
//       'booking_widget': 'Booking Widget',
//       'unknown': 'Unknown',
//       'detroit.gonano.com - get quote': 'Detroit Quote',
//       'detroit.gonano.com - shingles': 'Detroit Shingles',
//       'client free evaluation': 'Free Evaluation',
//       'find a dealer': 'Find a Dealer',
//       'guaranteed estimates': 'Guaranteed Estimates',
//       'gonano.com lead': 'Gonano Lead',
//       'gonano corp lead': 'Gonano Corp',
//       'albany website': 'Albany Website'
//     };

//     return nameMap[source.toLowerCase()] ||
//            source.split(/[-_]/).map(word =>
//              word.charAt(0).toUpperCase() + word.slice(1)
//            ).join(' ');
//   };

//   // Fetch data from API
//   const fetchLeadsData = async () => {
//     if (!startDate || !endDate || startDate > endDate) {
//       return;
//     }

//     setLoading(true);
//     setError(null);

//     const authToken = localStorage.getItem('locationAccessTokenContact');

//     if (!authToken) {
//       setError("No authorization token found");
//       setLoading(false);
//       return;
//     }

//     if (!locationId) {
//       setError("No location ID found in URL");
//       setLoading(false);
//       return;
//     }

//     const dateRange = {
//       gte: new Date(startDate.setHours(0, 0, 0, 0)).toISOString(),
//       lte: new Date(endDate.setHours(23, 59, 59, 999)).toISOString()
//     };

//     try {
//       const response = await fetch('http://localhost:5000/api/leadGenrateV2', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//           'Authorization': `Bearer ${authToken}`
//         },
//         body: JSON.stringify({
//           locationId: locationId,
//           pageLimit: 500,
//           page: 1,
//           filters: [
//             {
//               field: "dateAdded",
//               operator: "range",
//               value: dateRange
//             }
//           ],
//           sort: [
//             {
//               field: "dateAdded",
//               direction: "desc"
//             }
//           ]
//         })
//       });

//       if (!response.ok) {
//         throw new Error(`API error: ${response.status}`);
//       }

//       const result = await response.json();

//         // Process sources data for bar chart
//       if (result.sources) {
//         const sourcesArray = Object.entries(result.sources)
//           .map(([name, value]) => ({
//             name: formatSourceName(name),
//             value: parseInt(value),
//             originalName: name
//           }))
//           .sort((a, b) => b.value - a.value)
//           .slice(0, 10); // Show top 10 sources

//         setChartData(sourcesArray);

//         // Calculate total
//         const total = Object.values(result.sources).reduce((sum, val) => sum + parseInt(val), 0);
//         setTotalLeads(total);
//       }

//       setLoading(false);
//     } catch (err) {
//       console.error("Error fetching leads data:", err);
//       setError(err.message);
//       setLoading(false);
//     }
//   };

//   // Initial load
//   useEffect(() => {
//     if (startDate && endDate && locationId) {
//       fetchLeadsData();
//     }
//   }, [locationId]);

//   // Handle submit
//   const handleSubmit = () => {
//     if (startDate && endDate) {
//       fetchLeadsData();
//     }
//   };

//   // Custom tooltip
//   const CustomTooltip = ({ active, payload, label }) => {
//     if (active && payload && payload.length) {
//       const data = chartData.find(item => item.name === label);
//       return (
//         <Box
//           sx={{
//             backgroundColor: theme.palette.background.paper,
//             p: 2,
//             border: 1,
//             borderColor: 'divider',
//             borderRadius: 1,
//             boxShadow: 2,
//           }}
//         >
//           <Typography variant="body2" fontWeight="medium" mb={1}>
//             {label}
//           </Typography>
//           <Typography variant="body2" color="text.secondary">
//             Leads: {payload[0].value.toLocaleString()}
//           </Typography>
//           <Typography variant="caption" color="text.secondary">
//             {((payload[0].value / totalLeads) * 100).toFixed(1)}% of total
//           </Typography>
//         </Box>
//       );
//     }
//     return null;
//   };

//   // Truncate function for labels
//   const truncateLabel = (label, maxLength = 12) => {
//     return label.length > maxLength ? label.substring(0, maxLength) + '...' : label;
//   };

//   return (
//     <LocalizationProvider dateAdapter={AdapterDateFns}>
//       <Card
//         sx={{
//           backgroundColor: (theme) =>
//             theme.palette.mode === 'dark' ? theme.palette.grey[900] : '#ffffff',
//           boxShadow:
//             theme.palette.mode === 'dark'
//               ? '0 1px 3px rgba(0,0,0,0.5)'
//               : '0 1px 3px rgba(0,0,0,0.12)',
//           borderRadius: 2,
//         }}
//       >
//         <CardContent>
//           <Box sx={{ mb: 3 }}>
//             <Typography variant="h6" component="h2" fontWeight="bold">
//               Leads by Source
//             </Typography>
//             <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
//               Total leads in selected period: {totalLeads.toLocaleString()}
//             </Typography>

//             <Box
//               sx={{
//                 display: "flex",
//                 gap: 2,
//                 flexWrap: "wrap",
//                 alignItems: "flex-start"
//               }}
//             >
//               <DatePicker
//                 label="From Date"
//                 value={startDate}
//                 onChange={handleStartDateChange}
//                 maxDate={today}
//                 disabled={loading}
//                 slotProps={{
//                   textField: {
//                     sx: { minWidth: 180, flex: 1 },
//                     helperText: "Select start date"
//                   }
//                 }}
//               />

//               <DatePicker
//                 label="To Date"
//                 value={endDate}
//                 onChange={handleEndDateChange}
//                 minDate={startDate}
//                 maxDate={today}
//                 disabled={loading}
//                 slotProps={{
//                   textField: {
//                     sx: { minWidth: 180, flex: 1 },
//                     helperText: "Must be after start date"
//                   }
//                 }}
//               />

//               <Button
//                 variant="contained"
//                 onClick={handleSubmit}
//                 disabled={loading || !startDate || !endDate}
//                 sx={{
//                   minWidth: 100,
//                   height: 56,
//                   alignSelf: 'flex-start'
//                 }}
//               >
//                 Submit
//               </Button>
//             </Box>
//           </Box>

//           {loading && (
//             <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
//               <CircularProgress />
//             </Box>
//           )}

//           {error && (
//             <Alert severity="error" sx={{ mb: 2 }}>
//               {error}
//             </Alert>
//           )}

//           {!loading && !error && chartData.length > 0 && (
//             <ResponsiveContainer width="100%" height={400}>
//               <BarChart
//                 data={chartData}
//                 margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
//               >
//                 <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
//                 <XAxis
//                   dataKey="name"
//                   stroke={theme.palette.text.secondary}
//                   angle={0}
//                   textAnchor="middle"
//                   height={60}
//                   interval={0}
//                   tick={(props) => {
//                     const { x, y, payload } = props;
//                     const maxLength = 10; // Maximum characters to show
//                     const text = payload.value;
//                     const needsTruncation = text.length > maxLength;
//                     const displayText = needsTruncation ? text.substring(0, maxLength).trim() + '...' : text;

//                     return (
//                       <g transform={`translate(${x},${y})`}>
//                         {needsTruncation && <title>{text}</title>}
//                         <text
//                           x={0}
//                           y={0}
//                           dy={16}
//                           textAnchor="middle"
//                           fill={theme.palette.text.secondary}
//                           fontSize="11px"
//                           style={{
//                             cursor: needsTruncation ? 'help' : 'default',
//                             userSelect: 'none'
//                           }}
//                         >
//                           {displayText}
//                         </text>
//                       </g>
//                     );
//                   }}
//                 />
//                 <YAxis
//                   stroke={theme.palette.text.secondary}
//                   style={{ fontSize: '12px' }}
//                 />
//                 <Tooltip content={<CustomTooltip />} />
//                 <Bar dataKey="value" radius={[8, 8, 0, 0]}>
//                   {chartData.map((entry, index) => (
//                     <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
//                   ))}
//                 </Bar>
//               </BarChart>
//             </ResponsiveContainer>
//           )}

//           {!loading && !error && chartData.length === 0 && (
//             <Box sx={{ textAlign: 'center', py: 4 }}>
//               <Typography variant="body1" color="text.secondary">
//                 No leads found for the selected date range
//               </Typography>
//             </Box>
//           )}
//         </CardContent>
//       </Card>
//     </LocalizationProvider>
//   );
// }
