const express = require("express");
const cors = require("cors");
const bodyParser = require("body-parser");
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const router = express.Router();
const app = express();

const { findUserByEmail, saveTokens } = require("../models/UserModel");

const {
  generateAccessToken,
  generateRefreshToken,
  ACCESS_SECRET,
  REFRESH_SECRET,
} = require("../utils/token");

app.use(cors());
app.use(bodyParser.json());

const JWT_SECRET = "your-secret-key";

// GHL token validity checker
const isTokenValid = async (accessToken) => {
  try {
    const response = await fetch(
      "https://services.leadconnectorhq.com/oauth/locationToken",
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          Version: "2021-07-28",
        },
      }
    );
    return response.status === 200;
  } catch (error) {
    return false;
  }
};

// GHL token refresher
const refreshGhlToken = async (refreshToken) => {
  try {
    const data = new URLSearchParams();
    data.append("grant_type", "refresh_token");
    data.append("client_id", "67990c955eefdf2ff5493cfd-m9id3rb8");
    data.append("client_secret", "5046f197-49b1-4031-a5ae-88117a035caf");
    data.append("refresh_token", refreshToken);

    const response = await fetch(
      "https://services.leadconnectorhq.com/oauth/token",
      {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: data,
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to refresh token: ${response.statusText}`);
    }

    const responseData = await response.json();
    return responseData;
  } catch (error) {
    throw error;
  }
};

router.post("/login", async (req, res) => {
  const { email, password } = req.body;

  // ✅ Find user from DB
  const user = await findUserByEmail(email);
  if (!user) return res.status(404).json({ message: "User not found" });

  // ✅ Validate password
  // const isPasswordValid = password === user.password;
  const isPasswordValid = await bcrypt.compare(password, user.password);

  if (!isPasswordValid)
    return res.status(401).json({ message: "Invalid credentials" });

  // ✅ Generate app JWT access + refresh tokens
  const accessTokenApp = jwt.sign(
    { id: user.id, email: user.email, name: user.name },
    ACCESS_SECRET,
    { expiresIn: "24h" }
  );
  const refreshTokenApp = jwt.sign(
    { id: user.id, email: user.email, name: user.name },
    REFRESH_SECRET,
    { expiresIn: "365d" }
  );

  // ✅ Save them to DB
  await saveTokens(user.email, accessTokenApp, refreshTokenApp);

  try {
    // ⬇️ GHL code starts here (unchanged)
    const [rows] = await req.app.locals.db.execute(
      "SELECT * FROM ghl_data WHERE userId = ?",
      [user.id]
    );
    let ghlData = rows[0];
    let accessToken = ghlData?.accessToken;
    let refreshToken = ghlData?.refreshToken;

    if (ghlData) {
      const isValid = await isTokenValid(accessToken);
      if (!isValid && refreshToken) {
        try {
          const newTokenData = await refreshGhlToken(refreshToken);
          await req.app.locals.db.execute(
            `UPDATE ghl_data SET
              accessToken = ?, refreshToken = ?, expiresIn = ?, tokenType = ?,
              locationId = ?, companyId = ?, scope = ?, userType = ?, uniqueId = ?, rawResponse = ?
            WHERE userId = ?`,
            [
              newTokenData.access_token || null,
              newTokenData.refresh_token || null,
              newTokenData.expires_in || null,
              newTokenData.token_type || null,
              newTokenData.locationId || null,
              newTokenData.companyId || null,
              newTokenData.scope ? JSON.stringify(newTokenData.scope) : null,
              newTokenData.userType || null,
              newTokenData.uniqueId || null,
              JSON.stringify(newTokenData) || null,
              user.id,
            ]
          );

          const [updatedRows] = await req.app.locals.db.execute(
            "SELECT * FROM ghl_data WHERE userId = ?",
            [user.id]
          );
          ghlData = updatedRows[0];
          accessToken = newTokenData.access_token;
        } catch (refreshError) {
          console.error("Failed to refresh token:", refreshError);
          ghlData = null;
        }
      }
    }

    if (!ghlData || !accessToken) {
      const data = new URLSearchParams();
      data.append("grant_type", "authorization_code");
      data.append("client_id", "67990c955eefdf2ff5493cfd-m9id3rb8");
      data.append("client_secret", "5046f197-49b1-4031-a5ae-88117a035caf");
      data.append("code", "3ecaba9de085f647702ea9789b89df5b55c1c4e4");
      data.append(
        "redirect_uri",
        "http://localhost:5000/api/auth/oauth/callback"
      );

      const response = await fetch(
        "https://services.leadconnectorhq.com/oauth/token",
        {
          method: "POST",
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
          body: data,
        }
      );

      if (!response.ok) {
        console.log("error is:", response);
        throw new Error(`Failed to get GHL token: ${response.statusText}`);
      }

      const responseData = await response.json();
      await req.app.locals.db.execute(
        `INSERT INTO ghl_data (
          userId, accessToken, refreshToken, expiresIn, tokenType,
          locationId, companyId, scope, userType, uniqueId, rawResponse
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          user.id,
          responseData.access_token || null,
          responseData.refresh_token || null,
          responseData.expires_in || null,
          responseData.token_type || null,
          responseData.locationId || null,
          responseData.companyId || null,
          responseData.scope ? JSON.stringify(responseData.scope) : null,
          responseData.userType || null,
          responseData.uniqueId || null,
          JSON.stringify(responseData) || null,
        ]
      );

      const [newRows] = await req.app.locals.db.execute(
        "SELECT * FROM ghl_data WHERE userId = ?",
        [user.id]
      );
      ghlData = newRows[0];
    }

    let parsedScope;
    try {
      parsedScope = ghlData.scope ? JSON.parse(ghlData.scope) : null;
    } catch (e) {
      parsedScope = ghlData.scope;
    }

    // ✅ Final response with JWT + GHL
    res.json({
      message: "Login successful",
      ghlData: {
        accessToken: ghlData.accessToken || null,
        refreshToken: ghlData.refreshToken || null,
        expiresIn: ghlData.expiresIn || null,
        tokenType: ghlData.tokenType || null,
        locationId: ghlData.locationId || null,
        companyId: ghlData.companyId || null,
        scope: parsedScope,
        userType: ghlData.userType || null,
        uniqueId: ghlData.uniqueId || null,
        createdAt: ghlData.createdAt || null,
        updatedAt: ghlData.updatedAt || null,
      },
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        userAccessToken: accessTokenApp,
        userRefreshToken: refreshTokenApp,
      },
    });
  } catch (err) {
    console.log("error is here");
    console.error("Failed to handle GHL token:", err.message);
    res.status(500).json({
      message: "GHL Server Down",
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        userAccessToken: accessTokenApp,
        userRefreshToken: refreshTokenApp,
      },
    });
  }
});

module.exports = router;
