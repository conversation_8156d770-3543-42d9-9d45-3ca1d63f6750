// src/context/GHLContext.js
import React, { createContext, useState } from 'react';

export const GHLContext = createContext();

export const GHLProvider = ({ children }) => {
  const [token, setToken] = useState(null);
  const [locations, setLocations] = useState([]);

  return (
    <GHLContext.Provider value={{ token, setToken, locations, setLocations }}>
      {children}
    </GHLContext.Provider>
  );
};