import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api/ghl';

export const refreshAccessToken = async () => {
    const refreshToken = localStorage.getItem("ghl_refresh_token");
  
    if (!refreshToken) {
      console.error("No refresh token found, user needs to re-authenticate.");
      return null;
    }
  
    try {
      const response = await fetch("http://localhost:5000/api/auth/refresh", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ refresh_token: refreshToken }),
      });
  
      const data = await response.json();
  
      if (data.access_token) {
        console.log("Access token refreshed:", data.access_token);
        
        // Update stored tokens
        localStorage.setItem("ghl_access_token", data.access_token);
        localStorage.setItem("ghl_refresh_token", data.refresh_token);
        
        return data.access_token;
      } else {
        console.error("Failed to refresh access token:", data.error);
        return null;
      }
    } catch (error) {
      console.error("Error refreshing access token:", error);
      return null;
    }
  };
  