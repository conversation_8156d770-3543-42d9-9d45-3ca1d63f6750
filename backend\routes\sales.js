const express = require('express');
require('dotenv').config();

const router = express.Router();

// Route to fetch GHL sales data
router.get('/sales', async (req, res) => {
  try {
    const apiKey = process.env.GHL_API_KEY;
    const locationId = process.env.GHL_LOCATION_ID;
    const GHL_LOCATION__KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";

    if (!apiKey || !locationId) {
      return res.status(400).json({ error: 'API key or location ID not configured' });
    }

    const headers = {
      'Authorization': `Bearer ${GHL_LOCATION__KEY}`,
      'Content-Type': 'application/json',
      'Version': '2021-07-28'
    };

    const baseUrl = 'https://rest.gohighlevel.com';
    
    // Fetch pipelines
    const pipelinesResponse = await fetch(`${baseUrl}/v1/pipelines?locationId=${locationId}`, {
      method: 'GET',
      headers
    });
    const pipelinesData = await pipelinesResponse.json();

    if (!pipelinesData.pipelines || pipelinesData.pipelines.length === 0) {
      return res.json({ success: false, message: "No pipelines found", pipelineData: pipelinesData });
    }
    
    const pipeline = pipelinesData.pipelines[0];

    // Fetch opportunities
    const opportunitiesResponse = await fetch(
      `${baseUrl}/v1/pipelines/${pipeline.id}/opportunities`, 
      {
        method: 'GET',
        headers,
      }
    );
    const opportunitiesData = await opportunitiesResponse.json();

    const opportunities = opportunitiesData.opportunities || opportunitiesData.data || [];

    const calculateMonthlyRevenue = () => {
      const revenueMap = {};
      opportunities.forEach(opportunity => {
        const date = new Date(opportunity.createdAt);
        const monthYear = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
        revenueMap[monthYear] = (revenueMap[monthYear] || 0) + (opportunity.monetaryValue || 0);
      });
      return revenueMap;
    };

    return res.json({
      success: true,
      pipeline,
      opportunities,
      monthlyRevenue: calculateMonthlyRevenue(),
      total: opportunitiesData.meta?.total || 0,
    });
  } catch (error) {
    console.error('Error fetching GHL sales data:', error);
    res.status(500).json({ error: 'Failed to fetch sales data', details: error.message });
  }
});

// Test connection route
router.get('/test-connection', async (req, res) => {
  try {
    const apiKey = process.env.GHL_API_KEY;
    const locationId = process.env.GHL_LOCATION_ID;

    if (!locationId) {
      return res.status(400).json({ error: 'Location ID not configured' });
    }

    const headers = {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'Version': '2021-07-28'
    };

    const response = await fetch(`https://rest.gohighlevel.com/v1/locations/${locationId}`, {
      method: 'GET',
      headers
    });
    const data = await response.json();

    res.json({
      success: true,
      message: "Connection to GHL API successful",
      locationData: data
    });
  } catch (error) {
    console.error('API connection test error:', error);
    res.status(500).json({ error: 'API connection test failed', message: error.message });
  }
});

module.exports = router;
