const express = require("express");
const router = express.Router();
const dayjs = require("dayjs");
require("dotenv").config();

const app = express();
app.use(express.json());

// Commented out appointments API endpoint and function
// const APPOINTMENTS_API = "https://services.leadconnectorhq.com/appointments";

router.post("/conversion-funnel", async (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Missing Authorization header" });
  }

  const {
    locationId,
    pageLimit = 100,
    page = 1,
    filters = [],
    sort = []
  } = req.body;

  if (!locationId) {
    return res.status(400).json({ error: "Missing locationId in request body" });
  }

  try {
    const url = 'https://services.leadconnectorhq.com/contacts/search';

    // Extract date range from filters for logging
    const dateFilter = filters.find(f => f.field === 'dateAdded' && f.operator === 'range');
    if (dateFilter) {
      console.log('=== DATE RANGE FILTER APPLIED ===');
      console.log('From:', dateFilter.value.gte);
      console.log('To:', dateFilter.value.lte);
      console.log('================================');
    }

    // First, fetch the requested page of contacts
    const options = {
      method: 'POST',
      headers: {
        Authorization: authHeader,
        Version: '2021-07-28',
        'Content-Type': 'application/json',
        Accept: 'application/json'
      },
      body: JSON.stringify({
        locationId,
        pageLimit,
        page,
        filters,
        sort
      })
    };

    console.log('Making GHL API request with filters:', JSON.stringify(filters, null, 2));

    const response = await fetch(url, options);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const contacts = data.contacts || [];
    const totalContacts = data.total || 0;
    const hasMore = (page * pageLimit) < totalContacts;

    // Count sources ONLY from contacts within the filtered date range
    let allSources = {};
    let sourcePage = 1;
    let sourceHasMore = true;
    const sourcePageLimit = 500;
    let totalContactsCounted = 0;

    console.log(`Starting to count sources for ${totalContacts} contacts within date range...`);

    while (sourceHasMore) {
      // Use the EXACT same filters including date range
      const sourceOptions = {
        method: 'POST',
        headers: {
          Authorization: authHeader,
          Version: '2021-07-28',
          'Content-Type': 'application/json',
          Accept: 'application/json'
        },
        body: JSON.stringify({
          locationId,
          pageLimit: sourcePageLimit,
          page: sourcePage,
          filters: filters, // This includes the date range filter!
          sort
        })
      };

      const sourceResponse = await fetch(url, sourceOptions);
      const sourceData = await sourceResponse.json();

      if (!sourceResponse.ok) {
        console.error('Error fetching source data:', sourceResponse.status);
        break;
      }

      const sourceContacts = sourceData.contacts || [];

      // Log first contact's date to verify filtering is working
      if (sourcePage === 1 && sourceContacts.length > 0) {
        console.log('First contact dateAdded:', sourceContacts[0].dateAdded || sourceContacts[0].createdAt);
        console.log('Last contact dateAdded:', sourceContacts[sourceContacts.length - 1].dateAdded || sourceContacts[sourceContacts.length - 1].createdAt);
      }

      // Count sources from this page
      sourceContacts.forEach(contact => {
        totalContactsCounted++;

        // Double-check the date is within range (for debugging)
        const contactDate = new Date(contact.dateAdded || contact.createdAt);
        if (dateFilter) {
          const startDate = new Date(dateFilter.value.gte);
          const endDate = new Date(dateFilter.value.lte);

          if (contactDate < startDate || contactDate > endDate) {
            console.warn(`Contact outside date range! Date: ${contactDate}, Range: ${startDate} - ${endDate}`);
          }
        }

        // Get source
        let source = contact.source ||
          contact.attributionSource ||
          contact.contactSource ||
          (contact.customField && contact.customField.source) ||
          (contact.customFields && contact.customFields.source) ||
          'unknown';

        // Normalize source name
        source = source.toLowerCase();

        // Increment count
        if (allSources[source]) {
          allSources[source] = (parseInt(allSources[source]) + 1).toString();
        } else {
          allSources[source] = "1";
        }
      });

      // Check if there are more pages
      sourceHasMore = (sourcePage * sourcePageLimit) < sourceData.total;
      sourcePage++;

      console.log(`Page ${sourcePage - 1}: Counted ${sourceContacts.length} contacts`);
    }

    console.log(`\nTotal contacts counted: ${totalContactsCounted}`);
    console.log('Sources found:', allSources);

    // Verify the count matches
    const totalSourceCount = Object.values(allSources).reduce((sum, count) => sum + parseInt(count), 0);
    console.log(`Total from sources: ${totalSourceCount}, Total expected: ${totalContacts}`);

    // Return only total leads and qualified leads
    res.json({
      totalLeads: contacts.length,
      qualifiedLeads: contacts.filter(lead =>
        lead.opportunities && lead.opportunities.length > 0
      ).length,
      leadToQualifiedRate: contacts.length > 0 ? Math.round((contacts.filter(lead =>
        lead.opportunities && lead.opportunities.length > 0
      ).length / contacts.length) * 10000) / 100 : 0
    });
  } catch (error) {
    console.error("Error fetching contacts:", error);
    res.status(500).json({ error: "Failed to fetch contacts data" });
  }
});

// Commented out fetchAppointments function
// async function fetchAppointments(locationId, date, token) {
//   const url = new URL(APPOINTMENTS_API);
//   url.searchParams.append("locationId", locationId);
//   url.searchParams.append("startDate", `${date}T00:00:00Z`);
//   url.searchParams.append("endDate", `${date}T23:59:59Z`);
//   const response = await fetch(url.toString(), {
//     headers: {
//       Authorization: `Bearer ${token}`,
//     },
//   });
//   if (!response.ok) {
//     throw new Error(`Failed to fetch appointments: ${response.statusText}`);
//   }
//   const data = await response.json();
//   return data.appointments || [];
// }

module.exports = router;
