// src/components/Contacts.js
import React, { useEffect, useState, useRef } from "react";
import axios from "axios";
import { saveAs } from "file-saver";
import {
  alpha,
  Box,
  CssBaseline,
  Stack, 
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Typography,
  Checkbox,
  Chip,
  Avatar,
  Button,
  Menu,
  MenuItem,
  TextField,
  InputAdornment,
  Popover,
  List,
  ListItemText,
  Tooltip,
  Snackbar,
  Alert,
  useMediaQuery,
  Drawer,
  CircularProgress,
} from "@mui/material";
import Header from "../dashboard/components/Header";
import SideMenu from "../dashboard/components/SideMenu";
import AppNavbar from "../dashboard/components/AppNavbar";
import AppTheme from "../shared-theme/AppTheme";
import {
  // chartsCustomizations,
  dataGridCustomizations,
  datePickersCustomizations,
  treeViewCustomizations,
} from "../dashboard/theme/customizations/index";

import chartsCustomizations from '../dashboard/theme/customizations/charts'

// Icons
import SearchIcon from "@mui/icons-material/Search";
import AddIcon from "@mui/icons-material/Add";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import EmailIcon from "@mui/icons-material/Email";
import PhoneIcon from "@mui/icons-material/Phone";
import ViewColumnIcon from "@mui/icons-material/ViewColumn";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import ArrowUpwardOutlinedIcon from "@mui/icons-material/ArrowUpwardOutlined";
import ArrowDownwardOutlinedIcon from "@mui/icons-material/ArrowDownwardOutlined";
import ContentCopyOutlinedIcon from "@mui/icons-material/ContentCopyOutlined";
import KeyboardArrowDownOutlinedIcon from "@mui/icons-material/KeyboardArrowDownOutlined";
import CloseOutlinedIcon from "@mui/icons-material/CloseOutlined";
import ManageContactDrawer from "./ManageContactDrawer";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import SmartToyIcon from "@mui/icons-material/SmartToy";
import SmsIcon from "@mui/icons-material/Sms";
import DeleteIcon from "@mui/icons-material/Delete";
import StarRoundedIcon from "@mui/icons-material/StarRounded";
import CloudDownloadOutlinedIcon from "@mui/icons-material/CloudDownloadOutlined";
import CloudUploadOutlinedIcon from "@mui/icons-material/CloudUploadOutlined";
import BusinessIcon from "@mui/icons-material/Business";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import MergeIcon from "@mui/icons-material/Merge";

const xThemeComponents = {
  ...chartsCustomizations,
  ...dataGridCustomizations,
  ...datePickersCustomizations,
  ...treeViewCustomizations,
};

const exportToCSV = (data, filenamePrefix = "filesaver-All") => {
  const headers = [
    "Name",
    "Phone",
    "Email",
    "Tags",
    "Created",
    "Last Activity",
  ];

  const csvRows = [];

  // Add headers
  csvRows.push(headers.join(","));

  // Add data rows
  data.forEach((item) => {
    const row = [
      `"${item.contactName || item.formattedName || ""}"`,
      `"${item.phone || ""}"`,
      `"${item.email || ""}"`,
      `"${item.tags?.join("; ") || ""}"`,
      `"${item.formattedCreated || item.dateAdded || ""}"`,
      `"${item.formattedLastActivity || item.dateUpdated || ""}"`,
    ];
    csvRows.push(row.join(","));
  });

  const csvContent = csvRows.join("\n");

  // Create formatted filename like: Apr_09_25_4_03_pm
  const now = new Date();
  const options = {
    month: "short",
    day: "2-digit",
    year: "2-digit",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  };
  const dateStr = now
    .toLocaleString("en-US", options)
    .replaceAll(",", "")
    .replaceAll(":", "_")
    .replaceAll(" ", "_");

  const fileName = `${filenamePrefix}_${dateStr}.csv`;

  // Create blob and trigger download
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  saveAs(blob, fileName);
};

const avatarColors = [
  "#81C784",
  "#BA68C8",
  "#CE93D8",
  "#AED581",
  "#7986CB",
  "#80CBC4",
];

const getRandomAvatarColor = () => {
  const index = Math.floor(Math.random() * avatarColors.length);
  return avatarColors[index];
};

const Contacts = (props) => {
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [selected, setSelected] = useState([]);
  const [columnsMenuAnchor, setColumnsMenuAnchor] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [totalContacts, setTotalContacts] = useState(0);
  const [loadedCount, setLoadedCount] = useState(0);
  const observerTarget = useRef(null);
  const loadingRef = useRef(false);
  const base_url = process.env.REACT_APP_API_BASE_URL;
  const NEW_API_URL = process.env.REACT_APP_API_BASE_URL;

  // Helper function to generate a color based on name
  const generateColor = (name) => {
    if (!name) return "#E0E0E0";

    const colors = [
      "#BA68C8",
      "#81C784",
      "#AED581",
      "#A1887F",
      "#CE93D8",
      "#80CBC4",
      "#7986CB",
      "#BA68C8",
      "#D4C19D",
      "#E0E0E0",
    ];

    // Use the sum of character codes as a simple hash
    const hash = name
      .split("")
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  // Helper function to format the lastActivity timestamp to a relative time
  const formatLastActivity = (timestamp) => {
    if (!timestamp) return "";

    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    // Convert to minutes and hours
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);

    if (minutes < 60) {
      return `${minutes} min ago`;
    } else if (hours < 24) {
      return `${hours} hours ago`;
    } else {
      const days = Math.floor(hours / 24);
      return `${days} days ago`;
    }
  };

  // Helper function to format the date
  const formatDate = (dateString) => {
    if (!dateString) return "";

    const date = new Date(dateString);
    const options = {
      month: "short",
      day: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };

    const formattedDate = date.toLocaleString("en-US", options);
    return `${formattedDate.replace(",", "")} (EDT)`;
  };

  // Helper function to format phone numbers
  const formatPhoneNumber = (phone) => {
    if (!phone) return "";

    // Remove all non-numeric characters
    const cleaned = phone.replace(/\D/g, "");

    // Format based on length
    if (cleaned.length === 10) {
      return `+1 ${cleaned.substring(0, 3)} ${cleaned.substring(3, 6)} ${cleaned.substring(6, 10)}`;
    } else if (cleaned.length === 11 && cleaned.startsWith("1")) {
      return `+1 ${cleaned.substring(1, 4)} ${cleaned.substring(4, 7)} ${cleaned.substring(7, 11)}`;
    }

    // Return original format if we can't parse it
    return phone;
  };

  // Helper function to get initials from name
  const getInitials = (firstName, lastName) => {
    if (!firstName && !lastName) return "";

    const firstInitial = firstName ? firstName.charAt(0).toUpperCase() : "";
    const lastInitial = lastName ? lastName.charAt(0).toUpperCase() : "";

    return `${firstInitial}${lastInitial}`;
  };

  const fetchContacts = async (isInitial = false) => {
    if (loadingRef.current) return; // Prevent multiple simultaneous fetches

    if (isInitial) {
      setLoading(true);
      loadingRef.current = true;
    } else {
      setLoadingMore(true);
      loadingRef.current = true;
    }

    const locationAccessTokenContact = localStorage.getItem("locationAccessTokenContact");
    const selectedLocationId = localStorage.getItem("selectedLocationId");

    if (!locationAccessTokenContact || !selectedLocationId) {
      console.error("❌ Missing location token or selected location ID");
      setLoading(false);
      setLoadingMore(false);
      loadingRef.current = false;
      return;
    }

    try {
      const page = isInitial ? 1 : currentPage + 1;
      console.log('Fetching contacts page:', page, 'with search query:', searchQuery);

      const res = await fetch(`${NEW_API_URL}/api/contacts`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${locationAccessTokenContact}`,
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          locationId: selectedLocationId,
          limit: 100,
          page: page,
          query: searchQuery // Pass the search query to the backend
        }),
      });

      const response = await res.json();

      if (!res.ok) {
        throw new Error(response.message || "API error");
      }

      const contactsList = response?.contacts || [];
      console.log(`✅ Fetched ${contactsList.length} contacts for page ${page}`);

      const formattedContacts = contactsList.map((contact) => {
        const initials = getInitials(contact.firstNameLowerCase, contact.lastNameLowerCase);
        const fullName =
          contact.contactName ||
          `${contact.firstNameLowerCase || ""} ${contact.lastNameLowerCase || ""}`.trim();

        return {
          ...contact,
          formattedName: fullName,
          initials: initials,
          formattedPhone: formatPhoneNumber(contact.phone),
          formattedCreated: formatDate(contact.dateAdded),
          formattedLastActivity: formatLastActivity(contact.dateUpdated),
          color: generateColor(fullName),
        };
      });

      if (isInitial) {
        setContacts(formattedContacts);
        setLoadedCount(response.meta.loadedCount);
        setCurrentPage(1);
      } else {
        setContacts(prevContacts => {
          const existingIds = new Set(prevContacts.map(contact => contact.id));
          const uniqueNewContacts = formattedContacts.filter(
            contact => !existingIds.has(contact.id)
          );
          return [...prevContacts, ...uniqueNewContacts];
        });
        setLoadedCount(prevCount => prevCount + formattedContacts.length);
        setCurrentPage(page);
      }

      setTotalContacts(response.meta.total);
      setHasMore(response.meta.hasMore);
      console.log("✅ Contacts Formatted Successfully");

    } catch (error) {
      console.error("❌ Error fetching contacts:", error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
      loadingRef.current = false;
    }
  };

  useEffect(() => {
    fetchContacts(true);
  }, [searchQuery]); // Re-fetch when searchQuery changes

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = contacts.map((n) => n.id);
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };

  const handleClick = (event, id) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      );
    }

    setSelected(newSelected);
  };

  const isSelected = (id) => selected.indexOf(id) !== -1;

  const handleColumnsMenuOpen = (event) => {
    setColumnsMenuAnchor(event.currentTarget);
  };

  const handleColumnsMenuClose = () => {
    setColumnsMenuAnchor(null);
  };

  const [orderBy, setOrderBy] = useState("");
  const [order, setOrder] = useState("asc");

  const handleSort = (field) => {
    const isAsc = orderBy === field && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(field);
  };

  const [moreOptionsAnchorEl, setMoreOptionsAnchorEl] = useState(null);

  const handleMoreOptionsClick = (event) => {
    setMoreOptionsAnchorEl(event.currentTarget);
  };

  const handleMoreOptionsClose = () => {
    setMoreOptionsAnchorEl(null);
  };

  const isMoreOptionsOpen = Boolean(moreOptionsAnchorEl);
  const moreOptionsPopoverId = isMoreOptionsOpen
    ? "more-options-popover"
    : undefined;

  const [copiedText, setCopiedText] = useState("");
  const [snackbarOpen, setSnackbarOpen] = useState(false);

  const handleCopyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    setCopiedText(text);
    setSnackbarOpen(true);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const [tagsMenuAnchorEl, setTagsMenuAnchorEl] = useState(null);
  const [tagsMenuOpenRowId, setTagsMenuOpenRowId] = useState(null);

  const sortedContacts = [...contacts].sort((a, b) => {
    const aField = a[orderBy]?.toLowerCase?.() || "";
    const bField = b[orderBy]?.toLowerCase?.() || "";
    return order === "asc"
      ? aField.localeCompare(bField)
      : bField.localeCompare(aField);
  });

  const [filter, setFilterOpen] = useState(false);

  const toggleFilterDrawer = (newOpen) => () => setFilterOpen(newOpen);
  const isSmallScreen = useMediaQuery("(max-width:600px)");

  const bulkActions = [
    { title: "Add", icon: <AddIcon /> },
    { title: "Pipeline Change", icon: <FilterAltIcon /> },
    { title: "Add to Automation", icon: <SmartToyIcon /> },
    { title: "Send SMS", icon: <SmsIcon /> },
    { title: "Send Email", icon: <EmailIcon /> },
    { title: "Add Tags", icon: <EmailIcon /> },
    { title: "Remove Tags", icon: <EmailIcon /> },
    { title: "Delete Contacts", icon: <DeleteIcon /> },
    { title: "Send Review Requests", icon: <StarRoundedIcon /> },
    {
      title: "Export Contacts",
      icon: <CloudUploadOutlinedIcon />,
      onClick: () => exportToCSV(contacts),
    },
    { title: "Import Contacts", icon: <CloudDownloadOutlinedIcon /> },
    { title: "Add/Edit to Company", icon: <BusinessIcon /> },
    { title: "Bulk WhatsApp", icon: <WhatsAppIcon /> },
    { title: "Merge up to 10 Contacts", icon: <MergeIcon /> },
  ];


  return (
    <AppTheme {...props} themeComponents={xThemeComponents}>
      <CssBaseline enableColorScheme />
      <Box sx={{ display: "flex" }}>
        <SideMenu />
        <AppNavbar />
        {/* Main content */}
        <Box
          component="main"
          sx={(theme) => ({
            flexGrow: 1,
            backgroundColor: theme.vars
              ? `rgba(${theme.vars.palette.background.defaultChannel} / 1)`
              : alpha(theme.palette.background.default, 1),
            overflow: "auto",
          })}
        >
          <Stack
            spacing={2}
            sx={{
              mt: { xs: 8, md: 0 },
              pb: 5,
            }}
          >
            <Header title="Contacts" />

            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                bgcolor: (theme) => theme.palette.mode === 'dark'
                  ? theme.palette.grey[800]
                  : "#f2f7fa",
                padding: 2,
                mt: "0 !important",
                pt: 2,
                mx: 0,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: "8px",
                  "& > *": { mx: 0.5 },
                  "& .MuiIconButton-root ": {
                    backgroundColor: (theme) => theme.palette.background.paper
                  },
                }}
              >
                {bulkActions.map((action, i) => (
                  <Tooltip key={i} title={action.title} arrow>
                    <IconButton
                      size="small"
                      color="inherit"
                      onClick={action.onClick}
                    >
                      {action.icon}
                    </IconButton>
                  </Tooltip>
                ))}
              </Box>
            </Box>
            <Box
              sx={{
                display: "flex",
                ml: "auto",
                mt: "0 !important",
                backgroundColor: (theme) => theme.palette.mode === 'dark'
                  ? theme.palette.grey[800]
                  : "#f2f7fa",
                pl: 2,
                // borderBottom: "1px solid #e0e0e0",
              }}
            >
              {/* <Button
                variant="contained"
                endIcon={<ViewColumnIcon />}
                onClick={handleColumnsMenuOpen}
              Owner
              >
                Columns
              </Button> */}

              <Menu
                anchorEl={columnsMenuAnchor}
                open={Boolean(columnsMenuAnchor)}
                onClose={handleColumnsMenuClose}
                PaperProps={{
                  sx: {
                    backgroundColor: (theme) => theme.palette.background.paper,
                    "& .MuiMenuItem-root": {
                      "&:hover": {
                        backgroundColor: (theme) => theme.palette.action.hover,
                      },
                    },
                  },
                }}
              >
                <MenuItem>Name</MenuItem>
                <MenuItem>Phone</MenuItem>
                <MenuItem>Email</MenuItem>
                <MenuItem>Created</MenuItem>
                <MenuItem>Last Activity</MenuItem>
                <MenuItem>Tags</MenuItem>
              </Menu>
              <TextField
                placeholder="Quick search"
                size="small"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                sx={{
                  width: "250px",
                  mr: 1,
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "8px",
                    backgroundColor: (theme) => theme.palette.background.paper,
                  },
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{
                        color: (theme) => theme.palette.text.secondary,
                        fontSize: "20px"
                      }} />
                    </InputAdornment>
                  ),
                }}
              />

              <Button
                variant="outlined"
                onClick={toggleFilterDrawer(true, "Button Click")}
              Owner
              >
                More Filters
              </Button>
            </Box>

            {/* Table */}
            <TableContainer
              sx={{
                bgcolor: (theme) => theme.palette.mode === 'dark'
                  ? theme.palette.background.default
                  : "#f2f7fa",
                pl: 2,
                mt: "0 !important",
                pt: 2,
                pr: 2,
              }}
            >
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <Table sx={{
                  minWidth: 750,
                  bgcolor: (theme) => theme.palette.background.paper
                }} size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell></TableCell>
                      <TableCell padding="checkbox">
                        <Checkbox
                          indeterminate={
                            selected.length > 0 &&
                            selected.length < contacts.length
                          }
                          checked={
                            contacts.length > 0 &&
                            selected.length === contacts.length
                          }
                          onChange={handleSelectAllClick}
                        />
                      </TableCell>
                      {[
                        "contactName",
                        "phone",
                        "email",
                        "dateAdded",
                        "lastActivity",
                      ].map((field, idx) => (
                        <TableCell
                          key={idx}
                          onClick={() => handleSort(field)}
                          sx={{ cursor: "pointer" }}
                        >
                          <Box sx={{ display: "flex", alignItems: "center" }}>
                            <Typography
                              variant="subtitle2"
                              sx={{ fontWeight: 600 }}
                            >
                              {field.charAt(0).toUpperCase() +
                                field.slice(1).replace(/([A-Z])/g, " $1")}
                            </Typography>
                            {orderBy === field &&
                              (order === "asc" ? (
                                <ArrowUpwardOutlinedIcon fontSize="small" />
                              ) : (
                                <ArrowDownwardOutlinedIcon fontSize="small" />
                              ))}
                          </Box>
                        </TableCell>
                      ))}
                      <TableCell>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                          Tags
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {contacts.length > 0 ? (
                      <>
                        {contacts.map((contact) => {
                          const isItemSelected = isSelected(contact.id);
                          return (
                            <TableRow
                              key={contact.id}
                              hover
                              selected={isItemSelected}
                              sx={{
                                "& .MuiTableCell-root": { padding: "6px 6px" },
                              }}
                            >
                              <TableCell>
                                <IconButton
                                  onClick={handleMoreOptionsClick}
                                  size="small"
                                  sx={{
                                    padding: 0,
                                    width: "16px",
                                    height: "16px",
                                    border: "none",
                                  }}
                                >
                                  <MoreVertIcon
                                    fontSize="inherit"
                                    sx={{ color: (theme) => theme.palette.text.secondary }}
                                  />
                                </IconButton>
                              </TableCell>
                              <TableCell padding="checkbox">
                                <Checkbox
                                  checked={isItemSelected}
                                  onClick={(event) =>
                                    handleClick(event, contact.id)
                                  }
                                />
                              </TableCell>
                              <TableCell sx={{ minWidth: "170px" }}>
                                <Box sx={{ display: "flex", alignItems: "center" }}>
                                  <Avatar
                                    sx={{
                                      bgcolor: contact.color,
                                      width: 32,
                                      height: 32,
                                      fontSize: "14px",
                                      fontWeight: 500,
                                      mr: 1.5,
                                    }}
                                  >
                                    {contact.initials}
                                  </Avatar>
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      fontWeight: 500,
                                      textTransform: "capitalize",
                                    }}
                                  >
                                    {contact.formattedName || contact.contactName}
                                  </Typography>
                                </Box>
                              </TableCell>
                              <TableCell sx={{ borderBottom: "none", minWidth: "150px" }}>
                                {contact.phone && (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      position: "relative",
                                      "&:hover .copy-icon": {
                                        opacity: 1,
                                      },
                                    }}
                                  >
                                    <PhoneIcon
                                      fontSize="small"
                                      sx={{ mr: 0.5, color: "#1976d2" }}
                                    />
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        position: "relative",
                                        cursor: "default",
                                        display: "flex",
                                        alignItems: "center",
                                      }}
                                    >
                                      {contact.phone}
                                      <Box
                                        className="copy-icon"
                                        sx={{
                                          display: "inline-flex",
                                          alignItems: "center",
                                          opacity: 0,
                                          transition: "opacity 0.3s",
                                          ml: 1,
                                        }}
                                      >
                                        <Tooltip title="Copy Phone" arrow>
                                          <IconButton
                                            size="small"
                                            onClick={() =>
                                              handleCopyToClipboard(contact.phone)
                                            }
                                            sx={{
                                              padding: 0,
                                              width: "16px",
                                              height: "16px",
                                              border: "none",
                                            }}
                                          >
                                            <ContentCopyOutlinedIcon fontSize="small" />
                                          </IconButton>
                                        </Tooltip>
                                      </Box>
                                    </Typography>
                                  </Box>
                                )}
                              </TableCell>
                              <TableCell sx={{ borderBottom: "none", minWidth: "230px" }}>
                                {contact.email && (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      position: "relative",
                                      "&:hover .copy-icon": {
                                        opacity: 1,
                                      },
                                    }}
                                  >
                                    <EmailIcon
                                      fontSize="small"
                                      sx={{ mr: 0.5, color: "#1976d2" }}
                                    />
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        position: "relative",
                                        cursor: "default",
                                        display: "flex",
                                        alignItems: "center",
                                      }}
                                    >
                                      {contact.email}
                                      <Box
                                        className="copy-icon"
                                        sx={{
                                          display: "inline-flex",
                                          alignItems: "center",
                                          opacity: 0,
                                          transition: "opacity 0.3s",
                                          ml: 1,
                                        }}
                                      >
                                        <Tooltip title="Copy Email" arrow>
                                          <IconButton
                                            size="small"
                                            onClick={() =>
                                              handleCopyToClipboard(contact.email)
                                            }
                                            sx={{
                                              padding: 0,
                                              width: "16px",
                                              height: "16px",
                                              border: "none",
                                            }}
                                          >
                                            <ContentCopyOutlinedIcon fontSize="small" />
                                          </IconButton>
                                        </Tooltip>
                                      </Box>
                                    </Typography>
                                  </Box>
                                )}
                              </TableCell>
                              <TableCell sx={{ minWidth: "210px" }}>
                                <Typography variant="body2" sx={{ color: (theme) => theme.palette.text.secondary }}>
                                  {contact.formattedCreated || contact.dateAdded}
                                </Typography>
                              </TableCell>
                              <TableCell sx={{ minWidth: "130px" }}>
                                {contact.formattedLastActivity || contact.lastActivity ? (
                                  <Chip
                                    label={contact.formattedLastActivity || contact.lastActivity}
                                    size="small"
                                    sx={{
                                      bgcolor: (theme) => theme.palette.mode === 'dark'
                                        ? theme.palette.primary.dark
                                        : "#e3f2fd",
                                      color: (theme) => theme.palette.mode === 'dark'
                                        ? theme.palette.primary.contrastText
                                        : "#1976d2",
                                      height: "24px",
                                      fontSize: "0.75rem",
                                    }}
                                  />
                                ) : (
                                  <Typography variant="body2" sx={{ color: (theme) => theme.palette.text.secondary }}>
                                    No Activities Found
                                  </Typography>
                                )}
                              </TableCell>
                              <TableCell>
                                {contact.tags && contact.tags.length > 0 && (
                                  <Box sx={{ display: "flex", gap: 0.5 }}>
                                    {contact.tags.slice(0, 2).map((tag, index) => (
                                      <Chip
                                        key={index}
                                        label={tag}
                                        size="small"
                                        sx={{
                                          bgcolor: (theme) => theme.palette.mode === 'dark'
                                            ? theme.palette.success.dark
                                            : "#e0f2f1",
                                          color: (theme) => theme.palette.mode === 'dark'
                                            ? theme.palette.success.contrastText
                                            : "#00897b",
                                          fontSize: "0.75rem",
                                        }}
                                      />
                                    ))}
                                    {contact.tags.length > 2 && (
                                      <Chip
                                        label={`+${contact.tags.length - 2}`}
                                        size="small"
                                        sx={{
                                          bgcolor: (theme) => theme.palette.mode === 'dark'
                                            ? theme.palette.primary.dark
                                            : "#e3f2fd",
                                          color: (theme) => theme.palette.mode === 'dark'
                                            ? theme.palette.primary.contrastText
                                            : "#1976d2",
                                          fontSize: "0.75rem",
                                        }}
                                      />
                                    )}
                                  </Box>
                                )}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                        <TableRow>
                          <TableCell colSpan={8} align="center" sx={{ py: 2 }}>
                            {loadingMore ? (
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                                <CircularProgress size={20} />
                                <Typography variant="body2" color="text.secondary">
                                  Loading more contacts...
                                </Typography>
                              </Box>
                            ) : hasMore ? (
                              <Button
                                variant="outlined"
                                onClick={() => fetchContacts(false)}
                                disabled={loadingMore}
                                sx={{
                                  textTransform: 'none',
                                  borderColor: (theme) => theme.palette.mode === 'dark'
                                    ? theme.palette.grey[600]
                                    : '#e0e0e0',
                                  color: (theme) => theme.palette.text.secondary,
                                  '&:hover': {
                                    borderColor: (theme) => theme.palette.mode === 'dark'
                                      ? theme.palette.grey[500]
                                      : '#bdbdbd',
                                    backgroundColor: (theme) => theme.palette.action.hover,
                                  },
                                }}
                              >
                                Load More Contacts ({loadedCount} of {totalContacts})
                              </Button>
                            ) : (
                              <Typography variant="body2" color="text.secondary">
                                All {totalContacts} contacts loaded
                              </Typography>
                            )}
                          </TableCell>
                        </TableRow>
                      </>
                    ) : (
                      <TableRow>
                        <TableCell colSpan={8} align="center" sx={{ py: 3 }}>
                          <Typography variant="body2" color="text.secondary">
                            No contacts found
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              )}
            </TableContainer>

            {/* Pagination */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                px: 2,
                py: 1.5,
                borderTop: (theme) => `1px solid ${theme.palette.divider}`,
                bgcolor: (theme) => theme.palette.background.paper,
              }}
            >
              <Typography variant="body2" sx={{ color: (theme) => theme.palette.text.secondary }}>
                Showing {loadedCount} of {totalContacts} contacts
              </Typography>
            </Box>
          </Stack>
        </Box>
      </Box>
      <Popover
        id={moreOptionsPopoverId}
        open={isMoreOptionsOpen}
        anchorEl={moreOptionsAnchorEl}
        onClose={handleMoreOptionsClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        PaperProps={{
          sx: {
            borderRadius: "8px",
            boxShadow: "0px 4px 20px rgba(0,0,0,0.1)",
            width: 200,
            p: 1,
          },
        }}
      >
        <List
          sx={{
            backgroundColor: (theme) => theme.palette.background.paper,
            gap: 0.5,
            "& .MuiList-root ": { gap: 4 },
            "& .MuiMenuItem-root ": {
              backgroundColor: (theme) => theme.palette.mode === 'dark'
                ? theme.palette.grey[800]
                : "#1976d21f",
              borderRadius: 0.5,
            },
            "& .MuiMenuItem-root:hover ": {
              backgroundColor: "#1976d2",
              color: "#fff",
            },
          }}
        >
          {[
            "Book Appointment",
            "Create opportunity",
            "Send Review Request",
            "Bulk Operations",
          ].map((option, index) => (
            <MenuItem key={index} onClick={handleMoreOptionsClose}>
              <ListItemText primary={option} />
            </MenuItem>
          ))}
        </List>
      </Popover>
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={2500}
        onClose={handleSnackbarClose}
        message={`Copied: ${copiedText}`}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert severity="success" sx={{ width: "100%" }}>
          Copied: {copiedText}
        </Alert>
      </Snackbar>
      <Drawer
        sx={{
          "& .MuiPaper-root.MuiDrawer-paper  ": {
            width: isSmallScreen ? "100%" : 400,
          },
        }}
        anchor="right"
        open={filter}
        onClose={toggleFilterDrawer(false)}
      >
        <ManageContactDrawer onClose={toggleFilterDrawer(false)} />
      </Drawer>
    </AppTheme>
  );
};

export default Contacts;
