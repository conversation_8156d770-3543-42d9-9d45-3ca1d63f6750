import * as React from "react";
import PropTypes from "prop-types";
import Avatar from "@mui/material/Avatar";
import Button from "@mui/material/Button";
import Divider from "@mui/material/Divider";
import Drawer, { drawerClasses } from "@mui/material/Drawer";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import LogoutRoundedIcon from "@mui/icons-material/LogoutRounded";
import NotificationsRoundedIcon from "@mui/icons-material/NotificationsRounded";
import MenuButton from "./MenuButton";
import MenuContent from "./MenuContent";
import CardAlert from "./CardAlert";
import SelectContent from "./SelectContent";
import { Box } from "@mui/material";
import { useEffect } from "react";

// 🔁 Idle timeout constant
const IDLE_TIMEOUT = 540 * 60 * 1000; // 15 mins
// const IDLE_TIMEOUT = 5 * 1000; // ⏱️ 10 seconds for testing

// 🔁 Declare timer
let idleTimer = null;

// 🔁 Custom hook directly inside the filelastPath
const useIdleLogout = (logoutFn) => {
  useEffect(() => {
    const resetTimer = () => {
      if (idleTimer) clearTimeout(idleTimer);

      idleTimer = setTimeout(() => {
        logoutFn(); // Auto logout on inactivity
      }, IDLE_TIMEOUT);
    };

    const events = ["mousemove", "keydown", "scroll", "click", "touchstart"];
    events.forEach((event) => window.addEventListener(event, resetTimer));
    resetTimer();

    return () => {
      if (idleTimer) clearTimeout(idleTimer);
      events.forEach((event) => window.removeEventListener(event, resetTimer));
    };
  }, [logoutFn]);
};

// 🔁 Logout function
const handleLogout = () => {
  const muiMode = localStorage.getItem("mui-mode");

  // Don't use localStorage for logout flag, use sessionStorage
  sessionStorage.setItem("logoutFlag", "true");

  localStorage.clear();

  if (muiMode !== null) {
    localStorage.setItem("mui-mode", muiMode);
  }

  window.location.reload();
};

function SideMenuMobile({ open, toggleDrawer }) {
  // 🔁 Use auto logout hook here
  useIdleLogout(handleLogout);

  const loginUSer = JSON.parse(localStorage.getItem("currentUser"));

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={toggleDrawer(false)}
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        [`& .${drawerClasses.paper}`]: {
          backgroundImage: "none",
          backgroundColor: "background.paper",
        },
      }}
    >
      <Stack
        sx={{
          maxWidth: "70dvw",
          height: "100%",
        }}
      >
        <Stack direction="row" sx={{ p: 2, pb: 0, gap: 1 }}>
          <Stack
            direction="row"
            sx={{ gap: 1, alignItems: "center", flexGrow: 1, p: 1 }}
          >
            <Avatar
              sizes="small"
              alt={loginUSer?.name}
              src="/profile.png"
              sx={{ width: 40, height: 40 }}
            />
            <Box sx={{ mr: "auto" }}>
              <Typography
                variant="body2"
                sx={{ fontWeight: 500, lineHeight: "16px" }}
              >
                {loginUSer?.name}
              </Typography>
              <Typography variant="caption" sx={{ color: "text.secondary" }}>
                {loginUSer?.email}
              </Typography>
            </Box>
          </Stack>
          <MenuButton showBadge>
            <NotificationsRoundedIcon />
          </MenuButton>
        </Stack>
        <Divider />
        <Stack sx={{ flexGrow: 1 }}>
          <Box
            sx={{
              display: "flex",
              mt: "calc(var(--template-frame-height, 0px) + 4px)",
              p: 1.5,
              width: "100%",
            }}
          >
            <SelectContent />
          </Box>
          <Divider />
          <MenuContent />
          <Divider />
        </Stack>
        {/* <CardAlert /> */}
        <Stack sx={{ p: 2 }}>
          <Button
            variant="outlined"
            onClick={() => handleLogout()}
            fullWidth
            startIcon={<LogoutRoundedIcon />}
          >
            Logout
          </Button>
        </Stack>
      </Stack>
    </Drawer>
  );
}

SideMenuMobile.propTypes = {
  open: PropTypes.bool,
  toggleDrawer: PropTypes.func.isRequired,
};

export default SideMenuMobile;
