// src/components/GHLAuth.js
import React, { useEffect, useState } from "react";
import axios from "axios";
const Users = () => {
  const [pipelines, setPipelines] = useState([]);
console.log("pipelines",pipelines);

  useEffect(() => {
    axios.get(`http://localhost:5000/api/users/?locationId=t5r1YBZkxDCWUO2FyDkN`)
      .then(response => setPipelines(response.data.contact))
      .catch(error => console.error("Error fetching pipelines:", error));
  }, []);

  return (
    <div>
      <h2>Pipeline List</h2>
      <ul>
        {pipelines?.map((pipeline) => (
          <li key={pipeline.id}>{pipeline.name}</li>
        ))}
      </ul>
    </div>
  );
};

export default Users;