const fs = require('fs');
const path = require('path');

const tokenFile = path.join(__dirname, 'token.json');

const saveToken = (key, token) => {
  let tokens = {};
  if (fs.existsSync(tokenFile)) {
    tokens = JSON.parse(fs.readFileSync(tokenFile));
  }
  tokens[key] = token;
  fs.writeFileSync(tokenFile, JSON.stringify(tokens, null, 2));
  console.log(tokens);
};

const getToken = (key) => {
  if (!fs.existsSync(tokenFile)) return null;
  const tokens = JSON.parse(fs.readFileSync(tokenFile));
  return tokens[key] || null;
};

module.exports = { saveToken, getToken };
