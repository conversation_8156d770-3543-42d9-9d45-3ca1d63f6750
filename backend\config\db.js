const mysql = require("mysql2/promise");

const connectDB = async () => {
  try {
    const connection = await mysql.createConnection({
      host: '*************',
      user: 'root',
      password: 'ghlintegration@gonano',
      database: 'ghldata'
      // user: process.env.DB_USER,
      // password: process.env.DB_PASS,
      // database: process.env.DB_NAME,
      // socketPath: process.env.DB_SOCKET_PATH,
    });

    console.log("MySQL Connected Successfully");
    return connection;
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
};

module.exports = connectDB;
