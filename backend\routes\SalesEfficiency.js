const express = require('express');
const router = express.Router();
const moment = require('moment');
require('dotenv').config();

// Node.js 18+ has fetch by default
router.get("/sales-efficiency", async (req, res) => {
  const { locationId, userId, status, assignedTo } = req.query;
  const authHeader = req.headers.authorization;

  if (!locationId || !authHeader) {
    return res.status(400).json({ error: "Missing locationId or Authorization header" });
  }

  try {
    // Step 1: Get all pipelines
    const pipelineRes = await fetch(`https://services.leadconnectorhq.com/opportunities/pipelines?locationId=${locationId}`, {
      headers: {
        Authorization: authHeader,
        Version: "2021-07-28",
        Accept: "application/json"
      }
    });

    const pipelineData = await pipelineRes.json();

    if (!pipelineRes.ok) {
      return res.status(pipelineRes.status).json({
        error: "Failed to fetch pipelines from GHL",
        details: pipelineData,
      });
    }

    const { pipelines } = pipelineData;

    if (!Array.isArray(pipelines)) {
      console.error("GHL API did not return a valid pipelines array:", pipelineData);
      return res.status(500).json({ error: "Received invalid data for pipelines from GHL." });
    }

    const finalData = [];

    // Step 2: For each pipeline, fetch opportunities
    for (const pipeline of pipelines) {
      let allOpportunities = [];
      let page = 1;
      let hasMore = true;

      while (hasMore) {
        const url = `https://services.leadconnectorhq.com/opportunities/search?location_id=${locationId}&pipeline_id=${pipeline.id}&limit=100&page=${page}`;
        const oppRes = await fetch(url, {
          headers: {
            Authorization: authHeader,
            Version: "2021-07-28",
            Accept: "application/json"
          }
        });

        if (!oppRes.ok) break;

        const oppData = await oppRes.json();
        allOpportunities.push(...(oppData?.opportunities || []));

        if (oppData?.meta?.total <= allOpportunities.length) {
          hasMore = false;
        } else {
          page++;
        }
      }

      // 🔍 Step 3: Apply filters (if given)
      let filtered = allOpportunities;

      if (userId) {
        filtered = filtered.filter(o => o.user_id === userId);
      }

      if (status) {
        filtered = filtered.filter(o => o.status?.toLowerCase() === status.toLowerCase());
      }

      if (assignedTo) {
        filtered = filtered.filter(o => o.assignee_id === assignedTo);
      }

      finalData.push({
        pipelineId: pipeline.id,
        pipelineName: pipeline.name,
        totalOpportunities: filtered.length,
        opportunities: filtered
      });
    }

    // Combine all filtered opportunities from all pipelines
    const allFilteredOpportunities = finalData.flatMap(p => p.opportunities);

    // Calculate metrics
    const wonDeals = allFilteredOpportunities.filter(o => o.status === 'won');

    const totalRevenue = wonDeals.reduce((sum, opp) => {
      const val = parseFloat(opp.monetaryValue);
      return sum + (isNaN(val) ? 0 : val);
    }, 0);

    const durations = wonDeals
      .filter(o => o.createdAt && o.updatedAt)
      .map(o => moment(o.updatedAt).diff(moment(o.createdAt), 'seconds'));

    const averageSalesDuration = durations.length
      ? Math.round(durations.reduce((a, b) => a + b, 0) / durations.length)
      : 0;

    let salesVelocity = 0;
    if (wonDeals.length > 0) {
      const first = new Date(Math.min(...wonDeals.map(o => new Date(o.createdAt))));
      const last = new Date(Math.max(...wonDeals.map(o => new Date(o.updatedAt))));
      const months = Math.max(1, (last - first) / (1000 * 60 * 60 * 24 * 30));
      salesVelocity = parseFloat((totalRevenue / months).toFixed(2));
    }

    // Send response
    res.json({
      locationId,
      // pipelines: finalData,
      totalRevenue,
      averageSalesDuration,
      salesVelocity,
      totalOpportunities: allFilteredOpportunities.length,
      totalWon: wonDeals.length
    });
  } catch (err) {
    console.error("Error in /sales-efficiency:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});
  

module.exports = router;





























// const express = require("express");
// const router = express.Router();

// // GET /api/sales-efficiency

// router.get("/sales-efficiency", async (req, res) => {
//   const { locationId, userId, status, assignedTo } = req.query;
//   const authHeader = req.headers.authorization;

//   if (!locationId || !authHeader) {
//     return res.status(400).json({ error: "Missing locationId or Authorization header" });
//   }

//   try {
//     // Step 1: Get all pipelines
//     const pipelineRes = await fetch(`https://services.leadconnectorhq.com/opportunities/pipelines?locationId=${locationId}`, {
//       headers: {
//         Authorization: authHeader,
//         Version: "2021-07-28",
//         Accept: "application/json"
//       }
//     });

//     const { pipelines } = await pipelineRes.json();

//     const finalData = [];

//     // Step 2: For each pipeline, fetch opportunities
//     for (const pipeline of pipelines) {
//       let allOpportunities = [];
//       let page = 1;
//       let hasMore = true;

//       while (hasMore) {
//         const url = `https://services.leadconnectorhq.com/opportunities/search?location_id=${locationId}&pipeline_id=${pipeline.id}&limit=100&page=${page}`;
//         const oppRes = await fetch(url, {
//           headers: {
//             Authorization: authHeader,
//             Version: "2021-07-28",
//             Accept: "application/json"
//           }
//         });

//         if (!oppRes.ok) break;

//         const oppData = await oppRes.json();
//         allOpportunities.push(...(oppData?.opportunities || []));

//         if (oppData?.meta?.total <= allOpportunities.length) {
//           hasMore = false;
//         } else {
//           page++;
//         }
//       }

//       // 🔍 Step 3: Apply filters (if given)
//       let filtered = allOpportunities;

//       if (userId) {
//         filtered = filtered.filter(o => o.user_id === userId);
//       }

//       if (status) {
//         filtered = filtered.filter(o => o.status?.toLowerCase() === status.toLowerCase());
//       }

//       if (assignedTo) {
//         filtered = filtered.filter(o => o.assignee_id === assignedTo);
//       }

//       finalData.push({
//         pipelineId: pipeline.id,
//         pipelineName: pipeline.name,
//         totalOpportunities: filtered.length,
//         opportunities: filtered
//       });
//     }

//     res.json({ locationId, pipelines: finalData });
//   } catch (err) {
//     console.error("Error in /sales-efficiency:", err);
//     res.status(500).json({ error: "Internal server error" });
//   }
// });

// // router.get("/sales-efficiency", async (req, res) => {
// //   const { locationId } = req.query;
// //   const authHeader = req.headers.authorization;
// //   console.log(
// //     "location query is:",
// //     locationId,
// //     "from Sales Efficciency Backend."
// //   );

// //   if (!locationId || !authHeader) {
// //     return res
// //       .status(400)
// //       .json({ error: "Missing locationId or Authorization header" });
// //   }

// //   try {
// //     // 1. Get Pipelines
// //     const pipelineRes = await fetch(
// //       `https://services.leadconnectorhq.com/opportunities/pipelines?locationId=${locationId}`,
// //       {
// //         headers: {
// //           Authorization: authHeader,
// //           Version: "2021-07-28",
// //           Accept: "application/json",
// //         },
// //       }
// //     );

// //     const { pipelines } = await pipelineRes.json();

// //     if (!pipelines) {
// //       return res.status(500).json({ error: "Failed to fetch pipelines" });
// //     }

// //     const finalData = [];

// //     // 2. For each pipeline, fetch opportunities
// //     for (const pipeline of pipelines) {
// //       let allOpportunities = [];
// //       let page = 1;
// //       let hasMore = true;

// //       while (hasMore) {
// //         const oppRes = await fetch(
// //           `https://services.leadconnectorhq.com/opportunities/search?location_id=${locationId}&pipeline_id=${pipeline.id}&limit=100&page=${page}`,
// //           {
// //             headers: {
// //               Authorization: authHeader,
// //               Version: "2021-07-28",
// //               Accept: "application/json",
// //             },
// //           }
// //         );

// //         if (!oppRes.ok) {
// //           break;
// //         }

// //         const oppData = await oppRes.json();
// //         allOpportunities.push(...(oppData?.opportunities || []));

// //         if (oppData?.meta?.total <= allOpportunities.length) {
// //           hasMore = false;
// //         } else {
// //           page++;
// //         }
// //       }

// //       finalData.push({
// //         pipelineId: pipeline.id,
// //         pipelineName: pipeline.name,
// //         totalOpportunities: allOpportunities.length,
// //         opportunities: allOpportunities,
// //       });
// //     }

// //     res.json({ locationId, pipelines: finalData });
// //   } catch (err) {
// //     console.error("Error in /sales-efficiency:", err);
// //     res.status(500).json({ error: "Internal server error" });
// //   }
// // });

// module.exports = router;

// router.get("/sales-efficiency", async (req, res) => {
//   const { locationId } = req.query;
//   const authHeader = req.headers.authorization;

//   if (!authHeader || !locationId) {
//     return res.status(400).json({ error: "Missing locationId or Authorization" });
//   }

//   try {
//     // Step 1: Fetch pipelines
//     const pipelineResponse = await fetch(
//       `https://services.leadconnectorhq.com/opportunities/pipelines?locationId=${locationId}`,
//       {
//         headers: {
//           Authorization: authHeader,
//           Accept: "application/json",
//           Version: "2021-07-28",
//         },
//       }
//     );

//     if (!pipelineResponse.ok) {
//       const err = await pipelineResponse.text();
//       return res.status(pipelineResponse.status).json({ error: "Pipeline fetch failed", details: err });
//     }

//     const pipelines = await pipelineResponse.json();

//     // Step 2: Fetch opportunities per pipeline
//     const finalData = [];

//     for (const pipeline of pipelines) {
//       const { id: pipelineId, name: pipelineName, stages } = pipeline;

//       let allOpportunities = [];
//       let page = 1;
//       let hasMoreData = true;

//       while (hasMoreData) {
//         const oppUrl = `https://services.leadconnectorhq.com/opportunities/search?location_id=${locationId}&limit=100&page=${page}&pipeline_id=${pipelineId}`;

//         const oppResponse = await fetch(oppUrl, {
//           method: "GET",
//           headers: {
//             Authorization: authHeader,
//             Accept: "application/json",
//             Version: "2021-07-28",
//           },
//         });

//         if (!oppResponse.ok) {
//           const errText = await oppResponse.text();
//           return res.status(oppResponse.status).json({ error: "Failed to fetch opportunities", details: errText });
//         }

//         const oppData = await oppResponse.json();

//         allOpportunities.push(...oppData.opportunities);
//         if (oppData.meta && allOpportunities.length >= oppData.meta.total) {
//           hasMoreData = false;
//         } else {
//           page++;
//         }
//       }

//       // Step 3: Group user-wise + stage-wise
//       const userWise = {};
//       const stageWise = {};

//       for (const opp of allOpportunities) {
//         const userId = opp.user_id || "unassigned";
//         const stageName = opp.stage || "No Stage";

//         // User-wise
//         if (!userWise[userId]) userWise[userId] = [];
//         userWise[userId].push(opp);

//         // Stage-wise
//         if (!stageWise[stageName]) stageWise[stageName] = [];
//         stageWise[stageName].push(opp);
//       }

//       finalData.push({
//         pipelineId,
//         pipelineName,
//         totalOpportunities: allOpportunities.length,
//         userWise,
//         stageWise,
//       });
//     }

//     return res.status(200).json({ data: finalData });

//   } catch (err) {
//     console.error("Sales Efficiency Error:", err);
//     return res.status(500).json({ error: "Something went wrong", details: err.message });
//   }
// });
