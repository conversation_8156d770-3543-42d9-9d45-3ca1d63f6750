import * as React from 'react';
import { useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import CommonChart from './CommonChart';
import { 
  processLocationDataByCountry, 
  processLocationDataByState, 
  timeFilters 
} from './utils/chartDataProcessor';

export default function SubAccountsGeoChart({ locationData }) {
  const [selectedFilter, setSelectedFilter] = useState(timeFilters[0]);
  const [geoType, setGeoType] = useState('country'); // 'country' or 'state'

  const { chartData, totalCount } = useMemo(() => {
    if (geoType === 'country') {
      return processLocationDataByCountry(locationData, selectedFilter);
    } else {
      return processLocationDataByState(locationData, selectedFilter);
    }
  }, [locationData, selectedFilter, geoType]);

  const handleFilterChange = (filter) => {
    setSelectedFilter(filter);
  };

  const handleGeoTypeChange = (type) => {
    setGeoType(type);
  };

  const stats = {
    value: totalCount,
    label: `${geoType === 'country' ? 'Countries' : 'States'}: ${chartData.length}`,
    trend: 'up',
    trendLabel: 'Active'
  };

  const subtitle = `Sub-accounts distribution by ${geoType} in the last ${selectedFilter.value} ${selectedFilter.unit}${selectedFilter.value > 1 ? 's' : ''}`;

  return (
    <CommonChart
      title={`Sub-accounts by ${geoType === 'country' ? 'Country' : 'State'}`}
      subtitle={subtitle}
      data={chartData}
      chartType="pie"
      height={300}
      showChartTypeToggle={false}
      showTimeFilter={true}
      timeFilters={timeFilters}
      selectedTimeFilter={selectedFilter}
      onTimeFilterChange={handleFilterChange}
      stats={stats}
      emptyMessage={`No ${geoType} data available for selected period`}
      slotProps={{
        legend: { 
          hidden: false,
          direction: 'column',
          position: { vertical: 'middle', horizontal: 'right' }
        },
      }}
      margin={{ left: 20, right: 150, top: 20, bottom: 20 }}
    >
      {/* Custom Geo Type Toggle */}
      <div style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={() => handleGeoTypeChange('country')}
            style={{
              padding: '6px 12px',
              border: geoType === 'country' ? '1px solid #1976d2' : '1px solid #ccc',
              backgroundColor: geoType === 'country' ? '#1976d2' : 'transparent',
              color: geoType === 'country' ? 'white' : '#666',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            Country
          </button>
          <button
            onClick={() => handleGeoTypeChange('state')}
            style={{
              padding: '6px 12px',
              border: geoType === 'state' ? '1px solid #1976d2' : '1px solid #ccc',
              backgroundColor: geoType === 'state' ? '#1976d2' : 'transparent',
              color: geoType === 'state' ? 'white' : '#666',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            State
          </button>
        </div>
      </div>
    </CommonChart>
  );
}

SubAccountsGeoChart.propTypes = {
  locationData: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string,
      email: PropTypes.string,
      dateAdded: PropTypes.string.isRequired,
      city: PropTypes.string,
      state: PropTypes.string,
      country: PropTypes.string,
    })
  ),
};