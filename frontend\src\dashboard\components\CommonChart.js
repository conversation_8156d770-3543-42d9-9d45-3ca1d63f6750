import * as React from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '@mui/material/styles';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import ButtonGroup from '@mui/material/ButtonGroup';
import Box from '@mui/material/Box';
import { BarChart } from '@mui/x-charts/BarChart';
import { LineChart } from '@mui/x-charts/LineChart';
import { PieChart } from '@mui/x-charts/PieChart';
import Chip from '@mui/material/Chip';

export default function CommonChart({
  title,
  subtitle,
  data,
  chartType = 'bar',
  height = 250,
  showChartTypeToggle = true,
  showTimeFilter = false,
  timeFilters = [],
  selectedTimeFilter,
  onTimeFilterChange,
  onDataFiltered,
  colors,
  xAxisConfig,
  seriesConfig,
  emptyMessage = 'No data available',
  stats,
  ...props
}) {
  const theme = useTheme();
  const [selectedChartType, setSelectedChartType] = React.useState(chartType);

  const defaultColors = [
    theme.palette.primary.main,
    theme.palette.primary.light,
    theme.palette.primary.dark,
  ];

  const chartColors = colors || defaultColors;

  const handleChartTypeChange = (type) => {
    setSelectedChartType(type);
  };

  const renderChart = () => {
    if (!data || data.length === 0) {
      return (
        <Box
          sx={{
            height,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'text.secondary'
          }}
        >
          <Typography variant="body2">{emptyMessage}</Typography>
        </Box>
      );
    }

    const commonProps = {
      colors: chartColors,
      height,
      margin: { left: 50, right: 20, top: 20, bottom: 60 },
      grid: { horizontal: true },
      slotProps: {
        legend: { hidden: true },
      },
      ...props
    };

    switch (selectedChartType) {
      case 'bar':
        return (
          <BarChart
            {...commonProps}
            xAxis={xAxisConfig || [{
              scaleType: 'band',
              data: data.map(item => item.x || item.label || item.month),
            }]}
            series={seriesConfig || [{
              data: data.map(item => item.y || item.value || item.count),
              label: title,
              color: theme.palette.primary.main,
            }]}
          />
        );

      case 'line':
        return (
          <LineChart
            {...commonProps}
            xAxis={xAxisConfig || [{
              scaleType: 'point',
              data: data.map(item => item.x || item.label || item.month),
            }]}
            series={seriesConfig || [{
              data: data.map(item => item.y || item.value || item.count),
              label: title,
              curve: 'linear',
              showMark: true,
              area: true,
            }]}
          />
        );

      case 'pie':
        return (
          <PieChart
            {...commonProps}
            series={seriesConfig || [{
              data: data.map(item => ({
                id: item.id || item.label,
                label: item.label,
                value: item.value || item.count,
              })),
              innerRadius: 60,
              outerRadius: 100,
              paddingAngle: 2,
            }]}
          />
        );

      default:
        return null;
    }
  };

  return (
    <Card variant="outlined" sx={{ width: '100%' }}>
      <CardContent>
        <Stack spacing={2}>
          {/* Header */}
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            flexWrap="wrap"
            gap={1}
          >
            <Typography component="h2" variant="subtitle2">
              {title}
            </Typography>
            {stats && (
              <Stack direction="row" spacing={1} alignItems="center">
                <Chip 
                  size="small" 
                  color="primary" 
                  label={stats.label}
                  variant="outlined"
                />
              </Stack>
            )}
          </Stack>

          {/* Stats */}
          {stats && (
            <Stack
              direction="row"
              sx={{
                alignContent: { xs: 'center', sm: 'flex-start' },
                alignItems: 'center',
                gap: 1,
              }}
            >
              <Typography variant="h4" component="p">
                {stats.value}
              </Typography>
              {stats.trend && (
                <Chip 
                  size="small" 
                  color={stats.trend === 'up' ? 'success' : stats.trend === 'down' ? 'error' : 'default'}
                  label={stats.trendLabel || stats.trend}
                />
              )}
            </Stack>
          )}

          {subtitle && (
            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
              {subtitle}
            </Typography>
          )}

          {/* Chart Type Toggle */}
          {showChartTypeToggle && (
            <Stack direction="row" spacing={1}>
              <ButtonGroup size="small" variant="outlined">
                <Button
                  variant={selectedChartType === 'bar' ? 'contained' : 'outlined'}
                  onClick={() => handleChartTypeChange('bar')}
                >
                  Bar
                </Button>
                <Button
                  variant={selectedChartType === 'line' ? 'contained' : 'outlined'}
                  onClick={() => handleChartTypeChange('line')}
                >
                  Line
                </Button>
                <Button
                  variant={selectedChartType === 'pie' ? 'contained' : 'outlined'}
                  onClick={() => handleChartTypeChange('pie')}
                >
                  Pie
                </Button>
              </ButtonGroup>
            </Stack>
          )}

          {/* Time Filter */}
          {showTimeFilter && timeFilters.length > 0 && (
            <ButtonGroup size="small" variant="outlined" sx={{ alignSelf: 'flex-start' }}>
              {timeFilters.map((filter) => (
                <Button
                  key={filter.label}
                  variant={selectedTimeFilter?.label === filter.label ? 'contained' : 'outlined'}
                  onClick={() => onTimeFilterChange?.(filter)}
                >
                  {filter.label}
                </Button>
              ))}
            </ButtonGroup>
          )}

          {/* Chart */}
          {renderChart()}
        </Stack>
      </CardContent>
    </Card>
  );
}

CommonChart.propTypes = {
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string,
  data: PropTypes.array,
  chartType: PropTypes.oneOf(['bar', 'line', 'pie']),
  height: PropTypes.number,
  showChartTypeToggle: PropTypes.bool,
  showTimeFilter: PropTypes.bool,
  timeFilters: PropTypes.array,
  selectedTimeFilter: PropTypes.object,
  onTimeFilterChange: PropTypes.func,
  onDataFiltered: PropTypes.func,
  colors: PropTypes.array,
  xAxisConfig: PropTypes.array,
  seriesConfig: PropTypes.array,
  emptyMessage: PropTypes.string,
  stats: PropTypes.shape({
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    label: PropTypes.string,
    trend: PropTypes.oneOf(['up', 'down', 'neutral']),
    trendLabel: PropTypes.string,
  }),
};