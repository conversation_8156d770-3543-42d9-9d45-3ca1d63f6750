const jwt = require("jsonwebtoken");

const ACCESS_SECRET = "your-access-secret";
const REFRESH_SECRET = "your-refresh-secret";

const generateAccessToken = (user) => {
  return jwt.sign({ email: user.email, id: user.id }, ACCESS_SECRET, {
    expiresIn: "24h",
  });
};

const generateRefreshToken = (user) => {
  return jwt.sign({ email: user.email, id: user.id }, REFRESH_SECRET, {
    expiresIn: "365d",
  });
};

module.exports = {
  generateAccessToken,
  generateRefreshToken,
  ACCESS_SECRET,
  REFRESH_SECRET,
};
