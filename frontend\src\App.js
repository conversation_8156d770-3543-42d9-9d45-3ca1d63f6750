// src/App.js
import React from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { GHLProvider } from "./context/GHLContext";

import Dashboard from "./dashboard/Dashboard";
import Locations from "./pages/Locations";
import Sales from "./pages/SalesData";
import Calendar from "./pages/Calendar";
import Users from "./pages/Users";
import Pipelines from "./pages/Pipelines";
import Contacts from "./pages/Contacts";
import Tasks from "./pages/Tasks";
import MonthlyRevenue from "./pages/MonthlyRevenue";
import RevenueDashboard from "./pages/RevenueDashboard";
import OpportunitiesList from "./pages/OpportunitiesList";
import Subscriptions from "./pages/Subscriptions";
import SignIn from "./pages/SignIn";
import SignUp from "./pages/SignUp";
import FunnelDashboard from "./pages/Funnel";
import Auth from "./pages/Auth";
import { AuthProvider } from "./context/AuthContext";
import ProtectedRoute from "./context/ProtectedRoute";
import PublicRoute from "./context/PublicRoute";
import SubAccountDetail from "./pages/SubAccountDetail";
import NotFoundPage from "./pages/NotFoundPage";
import SubAccountModal from "./components/SubAccountModal";
import TrackLastPath from "./utils/TrackLastPath";
import RedirectToLastPath from "./utils/RedirectToLastPath";

function App() {
  return (
    <AuthProvider>
      <GHLProvider>
        <BrowserRouter>
          <TrackLastPath />
          <RedirectToLastPath />

          <Routes>
            <Route path="/sign-in" element={<PublicRoute />}>
              <Route path="/sign-in" element={<SignIn />} />
            </Route>
            <Route path="/sign-up" element={<PublicRoute />}>
              <Route path="/sign-up" element={<SignUp />} />
            </Route>
            <Route path="/select-sub-account" element={<SubAccountModal />} />

            <Route path="/" element={<ProtectedRoute />}>
              <Route path="/:locationId" element={<Dashboard />} />
              <Route path="/:locationId/contacts" element={<Contacts />} />
              <Route
                path="/:locationId/opportunities"
                element={<OpportunitiesList />}
              />
              <Route path="/:locationId/calendar" element={<Calendar />} />
              <Route
                path="/:locationId/sub-account-detail"
                element={<SubAccountDetail />}
              />
            </Route>
            <Route path="/locations" element={<ProtectedRoute />}>
              <Route path="/locations" element={<Locations />} />
            </Route>
            <Route path="/pipelines" element={<ProtectedRoute />}>
              <Route path="/pipelines" element={<Pipelines />} />
            </Route>
            <Route path="/monthlyRevenue" element={<ProtectedRoute />}>
              <Route path="/monthlyRevenue" element={<MonthlyRevenue />} />
            </Route>
            <Route path="/revenue-dashboard" element={<ProtectedRoute />}>
              <Route path="/revenue-dashboard" element={<RevenueDashboard />} />
            </Route>
            <Route path="/subscriptions" element={<ProtectedRoute />}>
              <Route path="/subscriptions" element={<Subscriptions />} />
            </Route>
            <Route path="/funnel" element={<ProtectedRoute />}>
              <Route path="/funnel" element={<FunnelDashboard />} />
            </Route>
            {/* Catch-all route for 404 */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </BrowserRouter>
      </GHLProvider>
    </AuthProvider>
  );
}

export default App;
