// const express = require("express");
// const axios = require("axios");
// const router = express.Router();
// require("dotenv").config();

// const app = express();
// app.use(express.json());

// const GHL_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6InQ1cjFZQlpreERDV1VPMkZ5RGtOIiwiY29tcGFueV9pZCI6IjA5bnRJc1JieDRnQXhjaGJIcWU3IiwidmVyc2lvbiI6MSwiaWF0IjoxNjg1NzI3MjA5NjYxLCJzdWIiOiJ1c2VyX2lkIn0.-UcbUyP-4nxElkvgSfm8s_78bk0cWb3A44B_vZj-29s";

// // Middleware to set API headers
// const apiHeaders = {
//   Authorization: `Bearer ${GHL_API_KEY}`
// };

// // Get all pipelines
// router.get("/pipelines", async (req, res) => {
//     try {
//        const response = await axios.get("https://rest.gohighlevel.com/v1/pipelines/", {
//          headers: {
//            Authorization: `Bearer ${GHL_API_KEY}`,
//          },
//        });
   
//        res.json(response.data);
//      } catch (error) {
//        console.error("Error fetching locations:", error);
//        res.status(500).json({ error: "Failed to fetch location data" });
//      }
//   });
  


// module.exports = router;

const express = require("express");
const fs = require("fs");
const router = express.Router();
require("dotenv").config();

const API_BASE_URL = "https://rest.gohighlevel.com/v1";
const GHL_API_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6InQ1cjFZQlpreERDV1VPMkZ5RGtOIiwiY29tcGFueV9pZCI6IjA5bnRJc1JieDRnQXhjaGJIcWU3IiwidmVyc2lvbiI6MSwiaWF0IjoxNjg1NzI3MjA5NjYxLCJzdWIiOiJ1c2VyX2lkIn0.-UcbUyP-4nxElkvgSfm8s_78bk0cWb3A44B_vZj-29s";

const apiHeaders = {
  Authorization: `Bearer ${GHL_API_KEY}`,
};

// Load previous data
const previousDataPath = "./data.json";
let previousData = {};
if (fs.existsSync(previousDataPath)) {
  previousData = JSON.parse(fs.readFileSync(previousDataPath, "utf-8"));
}

// Fetch current pipeline data
router.get("/pipelines/growth", async (req, res) => {
  try {
    const response = await fetch(`${API_BASE_URL}/pipelines/`, {
      method: "GET",
      headers: apiHeaders,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const responseData = await response.json();
    const pipelines = responseData.pipelines;

    let growthRates = {};

    pipelines.forEach((pipeline) => {
      const pipelineId = pipeline.id;
      growthRates[pipelineId] = {};

      pipeline.stages.forEach((stage) => {
        const stageName = stage.name;

        const previousCount =
          previousData?.pipelines?.[pipelineId]?.[stageName] || 0;
        const currentCount = Math.floor(Math.random() * 100); // Mocking current leads (Replace with API data)

        let growthRate =
          previousCount > 0
            ? ((currentCount - previousCount) / previousCount) * 100
            : "No previous data";

        growthRates[pipelineId][stageName] = {
          previous: previousCount,
          current: currentCount,
          growthRate: growthRate,
        };
      });
    });

    // Save current data for next time
    fs.writeFileSync(
      previousDataPath,
      JSON.stringify({ date: new Date(), pipelines: growthRates }, null, 2)
    );

    res.json(growthRates);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
