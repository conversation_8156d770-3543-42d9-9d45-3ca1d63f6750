
// src/services/auth.js
import axios from "axios";

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL
export const authenticateUser = () => {
    window.location.href = "https://projectspace.in/gonano/oauth/callback"; // Redirects to GHL OAuth
  };
  
  export const getAccessToken = async (code) => {
    try {
      const response = await fetch(`https://projectspace.in/gonano/oauth/callback?code=${code}`);
      console.log("Access token response:", response);
      return response.json();
    } catch (error) {
      console.error("Error fetching access token:", error);
    }
  };
  
  export const getTransactions = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/ghl/transactions`);
      return response.data;
    } catch (error) {
      console.error("Error fetching transactions:", error);
      throw error;
    }
  };
  
