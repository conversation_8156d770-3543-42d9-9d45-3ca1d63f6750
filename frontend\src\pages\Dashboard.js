import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

const Dashboard = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const queryParams = new URLSearchParams(window.location.search);
    const ghlAuth = queryParams.get("ghl_auth");

    if (ghlAuth === "success") {
      console.log("GHL Authorization successful!");
      alert("GoHighLevel authorization completed successfully! You can now access all features.");

      // Clear the URL parameters
      window.history.replaceState({}, document.title, "/dashboard");

      // Redirect to the main dashboard or sub-account selection
      navigate('/select-sub-account');

    } else if (ghlAuth === "error") {
      console.error("GHL Authorization failed");
      alert("GoHighLevel authorization failed. Please try logging in again.");

      // Clear the URL parameters
      window.history.replaceState({}, document.title, "/dashboard");

      // Redirect back to login
      navigate('/sign-in');
    }

    // Legacy support for old token-based flow
    const accessToken = queryParams.get("access_token");
    const refreshToken = queryParams.get("refresh_token");

    if (accessToken && refreshToken) {
      console.log("Access Token Received:", accessToken);
      console.log("Refresh Token Received:", refreshToken);

      // ✅ Store tokens in localStorage
      localStorage.setItem("ghl_access_token", accessToken);
      localStorage.setItem("ghl_refresh_token", refreshToken);

      // ✅ Clear the URL parameters
      window.history.replaceState({}, document.title, "/dashboard");
    }
  }, [navigate]);

  return (
    <>
      <h1 className="text-2xl font-bold mb-6">Dashboard</h1>
      <button onClick={() => navigate("/locations")} className="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
        Go to location page
      </button>
    </>
  );
};

export default Dashboard;
