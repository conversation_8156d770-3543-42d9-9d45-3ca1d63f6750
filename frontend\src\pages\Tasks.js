import React, { useEffect, useState } from "react";
import axios from "axios";

const NEW_API_URL = process.env.REACT_APP_API_BASE_URL

const Tasks = () => {
  const [tasks, setTasks] = useState([]);
  const contactId = "8OOwfuM4RrQht9AAb4RZ";  // Replace with the actual contact ID

  useEffect(() => {
    // Fetch tasks for a specific contact from your Express API
    axios
      .get(`${NEW_API_URL}/api/locations`)
      .then((response) => {
        setTasks(response.data);  // Store tasks in state
      })
      .catch((error) => {
        console.error("Error fetching tasks:", error);
      });
  }, [contactId]);

  return (
    <div>
      <ul>
        {tasks?.length ? tasks?.map((task) => (
          <li key={task.id}>{task.name}</li> // Render task names
        )):
        <h2>No data found</h2>
        }
      </ul>
    </div>
  );
};

export default Tasks;
