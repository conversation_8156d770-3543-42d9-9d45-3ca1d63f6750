import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Paper,
  Divider,
  Chip,
  TextField
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';

const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5000/api';

const ConversionFunnel = () => {
  const [funnelData, setFunnelData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [locationId, setLocationId] = useState('');
  const [startDate, setStartDate] = useState(dayjs().subtract(30, 'day'));
  const [endDate, setEndDate] = useState(dayjs());
  const [locations, setLocations] = useState([]);

  // Fetch locations on component mount
  useEffect(() => {
    fetchLocations();
  }, []);

  // Fetch locations from GHL
  const fetchLocations = async () => {
    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        setError('No authentication token found. Please log in again.');
        return;
      }
      
      const response = await axios.get(`${API_BASE_URL}/locations`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setLocations(response.data.locations || response.data || []);
      if (response.data.locations?.length > 0) {
        setLocationId(response.data.locations[0].id);
      }
    } catch (err) {
      console.error('Error fetching locations:', err);
      setError('Failed to fetch locations. Please try again.');
    }
  };

  // Fetch conversion funnel data
  const fetchFunnelData = async () => {
    if (!locationId) {
      setError('Please select a location');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        setError('No authentication token found. Please log in again.');
        return;
      }
      
      const response = await axios.get(`${API_BASE_URL}/conversion-funnel`, {
        headers: { Authorization: `Bearer ${token}` },
        params: {
          locationId,
          startDate: startDate.format('YYYY-MM-DD'),
          endDate: endDate.format('YYYY-MM-DD')
        }
      });

      setFunnelData(response.data);
    } catch (err) {
      console.error('Error fetching funnel data:', err);
      if (err.response?.status === 401) {
        setError('Authentication failed. Please log in again.');
      } else if (err.response?.status === 404) {
        setError('No data found for the selected location and date range.');
      } else {
        setError(err.response?.data?.error || err.message || 'Failed to fetch funnel data');
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch data when location or dates change
  useEffect(() => {
    if (locationId) {
      fetchFunnelData();
    }
  }, [locationId, startDate, endDate]);

  const formatPercentage = (value) => {
    return `${value.toFixed(2)}%`;
  };

  const getConversionColor = (rate) => {
    if (rate >= 70) return 'success';
    if (rate >= 40) return 'warning';
    return 'error';
  };

  const getNoShowColor = (rate) => {
    if (rate <= 10) return 'success';
    if (rate <= 25) return 'warning';
    return 'error';
  };

  if (!funnelData && !loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Conversion Funnel
        </Typography>
        <Alert severity="info">
          Select a location and date range to view conversion funnel metrics.
        </Alert>
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Conversion Funnel
        </Typography>

        {/* Filters */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={3}>
              <FormControl fullWidth>
                <InputLabel>Location</InputLabel>
                <Select
                  value={locationId}
                  onChange={(e) => setLocationId(e.target.value)}
                  label="Location"
                >
                  {locations.map((location) => (
                    <MenuItem key={location.id} value={location.id}>
                      {location.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={3}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={setStartDate}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={setEndDate}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <Button
                variant="contained"
                onClick={fetchFunnelData}
                disabled={loading}
                fullWidth
              >
                {loading ? <CircularProgress size={20} /> : 'Refresh'}
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {funnelData && (
          <>
            {/* Overall Funnel Summary */}
            <Typography variant="h5" gutterBottom sx={{ mt: 3 }}>
              Overall Funnel Summary
            </Typography>
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} md={3}>
                <Card>
                  <CardContent>
                    <Typography color="textSecondary" gutterBottom>
                      Total Leads
                    </Typography>
                    <Typography variant="h4">
                      {funnelData.overallFunnel.totalLeads}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={3}>
                <Card>
                  <CardContent>
                    <Typography color="textSecondary" gutterBottom>
                      Qualified Leads
                    </Typography>
                    <Typography variant="h4">
                      {funnelData.overallFunnel.qualifiedLeads}
                    </Typography>
                    <Chip
                      label={formatPercentage(funnelData.overallFunnel.conversionRates.leadToQualified)}
                      color={getConversionColor(funnelData.overallFunnel.conversionRates.leadToQualified)}
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={3}>
                <Card>
                  <CardContent>
                    <Typography color="textSecondary" gutterBottom>
                      Appointments
                    </Typography>
                    <Typography variant="h4">
                      {funnelData.overallFunnel.appointments}
                    </Typography>
                    <Chip
                      label={formatPercentage(funnelData.overallFunnel.conversionRates.leadToAppointment)}
                      color={getConversionColor(funnelData.overallFunnel.conversionRates.leadToAppointment)}
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={3}>
                <Card>
                  <CardContent>
                    <Typography color="textSecondary" gutterBottom>
                      No-Shows
                    </Typography>
                    <Typography variant="h4">
                      {funnelData.overallFunnel.noShows}
                    </Typography>
                    <Chip
                      label={formatPercentage(funnelData.overallFunnel.conversionRates.noShowRate)}
                      color={getNoShowColor(funnelData.overallFunnel.conversionRates.noShowRate)}
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* Conversion Funnel Visualization */}
            <Typography variant="h5" gutterBottom>
              Conversion Funnel
            </Typography>
            <Paper sx={{ p: 3, mb: 4 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                {/* Lead to Qualified Lead */}
                <Box sx={{ 
                  width: '80%', 
                  maxWidth: 400, 
                  bgcolor: 'primary.main', 
                  color: 'white', 
                  p: 2, 
                  borderRadius: 2,
                  textAlign: 'center',
                  mb: 1
                }}>
                  <Typography variant="h6">Leads</Typography>
                  <Typography variant="h4">{funnelData.overallFunnel.totalLeads}</Typography>
                </Box>
                
                <Box sx={{ mb: 1 }}>↓</Box>
                
                {/* Qualified Leads */}
                <Box sx={{ 
                  width: '60%', 
                  maxWidth: 300, 
                  bgcolor: 'success.main', 
                  color: 'white', 
                  p: 2, 
                  borderRadius: 2,
                  textAlign: 'center',
                  mb: 1
                }}>
                  <Typography variant="h6">Qualified Leads</Typography>
                  <Typography variant="h4">{funnelData.overallFunnel.qualifiedLeads}</Typography>
                  <Typography variant="body2">
                    {formatPercentage(funnelData.overallFunnel.conversionRates.leadToQualified)} conversion
                  </Typography>
                </Box>
                
                <Box sx={{ mb: 1 }}>↓</Box>
                
                {/* Appointments */}
                <Box sx={{ 
                  width: '40%', 
                  maxWidth: 200, 
                  bgcolor: 'warning.main', 
                  color: 'white', 
                  p: 2, 
                  borderRadius: 2,
                  textAlign: 'center',
                  mb: 1
                }}>
                  <Typography variant="h6">Appointments</Typography>
                  <Typography variant="h4">{funnelData.overallFunnel.appointments}</Typography>
                  <Typography variant="body2">
                    {formatPercentage(funnelData.overallFunnel.conversionRates.leadToAppointment)} conversion
                  </Typography>
                </Box>
                
                <Box sx={{ mb: 1 }}>↓</Box>
                
                {/* No-Shows */}
                <Box sx={{ 
                  width: '20%', 
                  maxWidth: 100, 
                  bgcolor: 'error.main', 
                  color: 'white', 
                  p: 2, 
                  borderRadius: 2,
                  textAlign: 'center'
                }}>
                  <Typography variant="h6">No-Shows</Typography>
                  <Typography variant="h4">{funnelData.overallFunnel.noShows}</Typography>
                  <Typography variant="body2">
                    {formatPercentage(funnelData.overallFunnel.conversionRates.noShowRate)} rate
                  </Typography>
                </Box>
              </Box>
            </Paper>

            {/* Daily Breakdown */}
            <Typography variant="h5" gutterBottom>
              Daily Breakdown
            </Typography>
            <Paper sx={{ p: 2 }}>
              <Grid container spacing={2}>
                {funnelData.dailyData.map((day, index) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          {dayjs(day.date).format('MMM DD, YYYY')}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Leads: {day.metrics.totalLeads}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Qualified: {day.metrics.qualifiedLeads} 
                          ({formatPercentage(day.metrics.conversionRates.leadToQualified)})
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Appointments: {day.metrics.appointments}
                          ({formatPercentage(day.metrics.conversionRates.leadToAppointment)})
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          No-Shows: {day.metrics.noShows}
                          ({formatPercentage(day.metrics.conversionRates.noShowRate)})
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Paper>
          </>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default ConversionFunnel;