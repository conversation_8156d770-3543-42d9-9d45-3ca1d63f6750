import React from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Container 
} from '@mui/material';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { useNavigate } from 'react-router';

const theme = createTheme({
  palette: {
    background: {
      default: '#f5f5f5',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  },
});

export default function NotFoundPage() {
  const navigate = useNavigate();

  const handleBackToHome = () => {
    // Add navigation logic here
    navigate('/')
    console.log('Navigate to home');
  };

  return (
    <ThemeProvider theme={theme}>
      <Box
        sx={{
          minHeight: '100vh',
          backgroundColor: '#f5f5f5',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          padding: { xs: 2, sm: 3, md: 4 },
        }}
      >
        <Container
          maxWidth="md"
          sx={{
            textAlign: 'center',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: { xs: 2, sm: 3, md: 4 },
          }}
        >
          <Typography
            variant="h1"
            component="h1"
            sx={{
              fontSize: { 
                xs: '2.5rem', 
                sm: '3.5rem', 
                md: '4.5rem', 
                lg: '5.5rem' 
              },
              fontWeight: 400,
              color: '#333333',
              lineHeight: 1.2,
              letterSpacing: '-0.02em',
              maxWidth: '800px',
              margin: 0,
              padding: 0,
            }}
          >
            We're working on it.
          </Typography>
          
          <Typography
            variant="body1"
            component="p"
            sx={{
              fontSize: { xs: '1rem', sm: '1.1rem', md: '1.125rem' },
              color: '#666666',
              fontWeight: 400,
              lineHeight: 1.5,
              maxWidth: '500px',
              margin: { xs: '16px 0 24px 0', sm: '20px 0 28px 0', md: '24px 0 32px 0' },
            }}
          >
            This page is not available at the moment. Please try again later.
          </Typography>
          
          <Button
            variant="contained"
            onClick={handleBackToHome}
            sx={{
              backgroundColor: '#333333',
              color: '#ffffff',
              fontSize: { xs: '0.875rem', sm: '0.9375rem', md: '1rem' },
              fontWeight: 500,
              textTransform: 'none',
              borderRadius: '6px',
              padding: { 
                xs: '10px 20px', 
                sm: '12px 24px', 
                md: '14px 28px' 
              },
              minWidth: { xs: '140px', sm: '160px' },
              boxShadow: 'none',
              '&:hover': {
                backgroundColor: '#4d4d4d',
                boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
              },
              '&:active': {
                backgroundColor: '#1a1a1a',
              },
              '&:focus': {
                outline: '2px solid #666666',
                outlineOffset: '2px',
              },
            }}
          >
            Back to home
          </Button>
        </Container>
      </Box>
    </ThemeProvider>
  );
}