import { useEffect } from "react";
import { useLocation } from "react-router-dom";

const publicPaths = ["/", "/sign-in", "/sign-up", "/select-sub-account"];

const isValidProtectedPath = (pathname) => {
  const segments = pathname.split("/").filter(Boolean);
  return segments.length >= 1;
};

const TrackLastPath = () => {
  const location = useLocation();

  useEffect(() => {
    // Check sessionStorage logoutFlag
    const isLoggingOut = sessionStorage.getItem("logoutFlag") === "true";

    if (isLoggingOut) {
      sessionStorage.removeItem("logoutFlag"); // clean it after first load
      return;
    }

    if (!publicPaths.includes(location.pathname) && isValidProtectedPath(location.pathname)) {
      localStorage.setItem("lastPath", location.pathname);
    }
  }, [location.pathname]);

  return null;
};

export default TrackLastPath;
