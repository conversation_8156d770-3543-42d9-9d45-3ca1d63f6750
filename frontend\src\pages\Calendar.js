// src/components/GHLAuth.js
import React, { useEffect, useState } from "react";
import axios from "axios";
const Calendar = () => {
  const [pipelines, setPipelines] = useState([]);
console.log("pipelines",pipelines);

const NEW_API_URL = process.env.REACT_APP_API_BASE_URL;

  useEffect(() => {
    axios.get(`${NEW_API_URL}/api/calendars/services?teamId=Y8AQ37ir9jBUvUsvcNUn`)
      .then(response => setPipelines(response.data.contact))
      .catch(error => console.error("Error fetching pipelines:", error));
  }, []);

  return (
    <div>
      <h2>Pipeline List</h2>
      <ul>
        {pipelines?.map((pipeline) => (
          <li key={pipeline.id}>{pipeline.name}</li>
        ))}
      </ul>
    </div>
  );
};

export default Calendar;