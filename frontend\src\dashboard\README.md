# Dashboard template

## Usage

<!-- #repo-reference -->

1. Copy these folders (`dashboard` and `shared-theme`) into your project, or one of the [example projects](https://github.com/mui/material-ui/tree/master/examples).
2. Make sure your project has the required dependencies: @mui/material, @mui/icons-material, @emotion/styled, @emotion/react, @mui/x-charts, @mui/x-date-pickers, @mui/x-data-grid, @mui/x-tree-view, dayjs
3. Import and use the `Dashboard` component.

## Demo

<!-- #host-reference -->

View the demo at https://next.mui.com/material-ui/getting-started/templates/dashboard/.
