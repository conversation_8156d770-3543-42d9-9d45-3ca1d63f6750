export const mockFields = [
  { id: "owner", label: "Owner" },
  { id: "followers", label: "Followers" },
  { id: "status", label: "Status" },
  { id: "campaignType", label: "Campaign type" },
  { id: "lastStageChangeDate", label: "Last stage change date" },
  { id: "lastStatusChangeDate", label: "Last status change date" },
  { id: "createdOn", label: "Created on" },
  { id: "opportunityValue", label: "Opportunity Value" },
  { id: "updatedOn", label: "Updated on" },
  { id: "opportunityWonOn", label: "Opportunity won on" },
  { id: "opportunityLostOn", label: "Opportunity lost on" },
];

export const menuOptions = {
  opportunityValue: [
    { id: "0-5000", label: "$0 - $5,000" },
    { id: "5001-10000", label: "$5,001 - $10,000" },
    { id: "10001-20000", label: "$10,001 - $20,000" },
    { id: "20001-30000", label: "$20,001 - $30,000" },
    { id: "30001-40000", label: "$30,001 - $40,000" },
    { id: "40001-50000", label: "$40,001 - $50,000" },
    { id: "50001+", label: "$50,001+" },
  ],
  lastStatusChangeDate: [
    { id: "lastWeek", label: "Last week" },
    { id: "lastMonth", label: "Last month" },
    { id: "lastQuarter", label: "Last quarter" },
    { id: "lastYear", label: "Last year" },
    { id: "allTime", label: "All time" },
  ],
  createdOn: [
    { id: "lastWeek", label: "Last week" },
    { id: "lastMonth", label: "Last month" },
    { id: "lastQuarter", label: "Last quarter" },
    { id: "lastYear", label: "Last year" },
    { id: "allTime", label: "All time" },
  ],
  updatedOn: [
    { id: "lastWeek", label: "Last week" },
    { id: "lastMonth", label: "Last month" },
    { id: "lastQuarter", label: "Last quarter" },
    { id: "lastYear", label: "Last year" },
    { id: "allTime", label: "All time" },
  ],
  opportunityWonOn: [
    { id: "lastWeek", label: "Last week" },
    { id: "lastMonth", label: "Last month" },
    { id: "lastQuarter", label: "Last quarter" },
    { id: "lastYear", label: "Last year" },
    { id: "allTime", label: "All time" },
  ],
  opportunityLostOn: [
    { id: "lastWeek", label: "Last week" },
    { id: "lastMonth", label: "Last month" },
    { id: "lastQuarter", label: "Last quarter" },
    { id: "lastYear", label: "Last year" },
    { id: "allTime", label: "All time" },
  ],
  owner: [
    { id: "john_Doe", label: "john Doe" },
    { id: "jane_doe", label: "Jane Doe" },
    { id: "bill_gates", label: "Bill Gates" },
    { id: "steve_jobs", label: "Steve Jobs" },
    { id: "bill_maher", label: "Bill Maher" },
    { id: "bill_gates_jr", label: "Bill Gates Jr." },
    { id: "bill_gates_ii", label: "Bill Gates II" },
    { id: "bill_gates_iii", label: "Bill Gates III" },
    { id: "bill_gates_iv", label: "Bill Gates IV" },
    { id: "bill_gates_v", label: "Bill Gates V" },
    { id: "bill_gates_vi", label: "Bill Gates VI" },
    { id: "bill_gates_vii", label: "Bill Gates VII" },
    { id: "bill_gates_viii", label: "Bill Gates VIII" },
    { id: "bill_gates_ix", label: "Bill Gates IX" },
    { id: "bill_gates_x", label: "Bill Gates X" },
    { id: "bill_gates_xi", label: "Bill Gates XI" },
    { id: "bill_gates_xii", label: "Bill Gates XII" },
    { id: "bill_gates_xiii", label: "Bill Gates XIII" },
    { id: "bill_gates_xiv", label: "Bill Gates XIV" },
    { id: "bill_gates_xv", label: "Bill Gates XV" },
    { id: "bill_gates_xvi", label: "Bill Gates XVI" },
  ],

  followers: [
    { id: "john_Doe", label: "john Doe" },
    { id: "jane_doe", label: "Jane Doe" },
    { id: "bill_gates", label: "Bill Gates" },
    { id: "steve_jobs", label: "Steve Jobs" },
    { id: "bill_maher", label: "Bill Maher" },
    { id: "bill_gates_jr", label: "Bill Gates Jr." },
    { id: "bill_gates_ii", label: "Bill Gates II" },
    { id: "bill_gates_iii", label: "Bill Gates III" },
    { id: "bill_gates_iv", label: "Bill Gates IV" },
    { id: "bill_gates_v", label: "Bill Gates V" },
    { id: "bill_gates_vi", label: "Bill Gates VI" },
    { id: "bill_gates_vii", label: "Bill Gates VII" },
    { id: "bill_gates_viii", label: "Bill Gates VIII" },
    { id: "bill_gates_ix", label: "Bill Gates IX" },
    { id: "bill_gates_x", label: "Bill Gates X" },
    { id: "bill_gates_xi", label: "Bill Gates XI" },
    { id: "bill_gates_xii", label: "Bill Gates XII" },
    { id: "bill_gates_xiii", label: "Bill Gates XIII" },
    { id: "bill_gates_xiv", label: "Bill Gates XIV" },
    { id: "bill_gates_xv", label: "Bill Gates XV" },

    { id: "bill_gates_xvi", label: "Bill Gates XVI" },
  ],

  status: [
    { id: "open", label: "Open" },
    { id: "won", label: "Won" },
    { id: "lost", label: "Lost" },
    { id: "abandon", label: "Abandon" },
  ],
  campaignType: [
    { id: "no_show_nurture", label: "B) No Show Nurture" },
    { id: "not_ready_yet", label: "C) NoT Ready Yet" },
    { id: "booking_requested_reply", label: "D) Booking Requested Reply" },
    { id: "appointment_reminder", label: "E) Appointment Reminder" },
  ],
  lastStageChangeDate: [
    { id: "yesterday", label: "Yesterday" },
    { id: "not_ready_yet", label: "Today" },
    { id: "booking_requested_reply", label: "This Week" },
    { id: "last_week", label: "Last Week" },
    { id: "last_7_days", label: "Last 7 Days" },
    { id: "last_30_days", label: "Last 30 Days" },
    { id: "this_month", label: "This Month" },
    { id: "last_month", label: "Last Month" },
    { id: "this_year", label: "This Year" },
    { id: "last_year", label: "Last Year" },
  ],
};

export const sortOptions = [
  { value: "created_on", label: "Created On" },
  { value: "updated_on", label: "Updated On" },
  { value: "last_stage_change", label: "Last Stage Change Date" },
  { value: "last_status_change", label: "Last Status Change Date" },
  { value: "opportunity_name", label: "Opportunity Name" },
  { value: "stage", label: "Stage" },
  { value: "status", label: "Status" },
  { value: "opportunity_source", label: "Opportunity Source" },
  { value: "opportunity_value", label: "Opportunity Value" },
  {
    value: "days_since_last_stage",
    label: "Days Since Last Stage Change Date",
  },
  {
    value: "days_since_last_status",
    label: "Days Since Last Status Change Date",
  },
  { value: "days_since_last_updated", label: "Days Since Last Updated" },
];

export const allFieldValues = [
  {
    "Other Details": [
      {
        label: "Amount",
        value: "2024-04-01",
      },
      {
        label: "Updated On (EDT)",
        value: "2024-04-01",
      },
      {
        label: "Last Status Change Date (EDT)",
        value: "2024-04-01",
      },
      {
        label: "Last Stage Change Date (EDT)",
        value: "2024-04-01",
      },
      {
        label: "Day Since Last Stage Change Date (EDT)",
        value: "2024-04-01",
      },
      {
        label: "Day Since Last Status Change Date (EDT)",
        value: "2024-04-01",
      },
      {
        label: "Day Since Last Updated (EDT)",
        value: "2024-04-01",
      },
    ],
    "Primary Contact Details": [
      {
        label: "Contact",
        value: "John Smith",
      },
      {
        label: "Business Name",
        value: "Tech Innovators Inc.",
      },
      {
        label: "Next Task Due Date",
        value: "2024-04-01",
      },
      {
        label: "Day Till Next Task Due Date",
        value: "2024-04-01",
      },
      {
        label: "Engagement Score",
        value: "$25,000",
      },
      {
        label: "Day Till Next Task Appointment Date",
        value: "2024-04-01",
      },
      {
        label: "Contact's Phone",
        value: "+****************",
      },
      {
        label: "Contact's Email",
        value: "mailto:<EMAIL>",
      },
    ],
    "Opportunity Details": [
      {
        label: "Pipeline",
        value: "Sales Funnel Q2",
      },
      {
        label: "Stage",
        value: "Negotiation",
      },
      {
        label: "Status",
        value: "Open",
      },
      {
        label: "Opportunity Value",
        value: "test value",
      },
      {
        label: "Opportunity Owner",
        value: "Jane Doe",
      },
      {
        label: "Opportunity Source",
        value: "JaOpportunity Name",
      },
      {
        label: "Lost Reason",
        value: "Budget Constraints",
      },
    ],
  },
];
