// actions/userActions.js
import axios from 'axios';
import { FETCH_LOCATIONS_REQUEST, FETCH_LOCATIONS_FAILURE, FETCH_LOCATIONS_SUCCESS } from '../../services/types';
import { useEffect, useState } from 'react';
const base_url = process.env.REACT_APP_API_BASE_URL
const newBase_url = "http://localhost:5000"
const NEW_API_URL = process.env.REACT_APP_API_BASE_URL;
// const newBase_url = "http://************:5000"
// Regular action creators
export const fetchUserRequest = () => ({
  type: FETCH_LOCATIONS_REQUEST
});

export const fetchUserSuccess = (locations) => ({
  type: FETCH_LOCATIONS_SUCCESS,
  payload: locations
});

export const fetchUserFailure = (error) => ({
  type: FETCH_LOCATIONS_FAILURE,
  payload: error
});

// Thunk action creator
export const fetchLocations = () => {
  return (dispatch) => {
    dispatch(fetchUserRequest());

    // Get both tokens from localStorage
    const ghlAccessToken = localStorage.getItem('ghlAccessToken');
    const ghlaccessToken = localStorage.getItem('ghlaccessToken');

    // Check if ghlAccessToken exists and is not undefined/null/empty
    let token = ghlAccessToken;
    if (!token || token === 'undefined' || token === 'null' || token === '') {
      // If ghlAccessToken is not valid, use ghlaccessToken
      token = ghlaccessToken;
    }

    // If no valid token is found, dispatch error
    if (!token || token === 'undefined' || token === 'null' || token === '') {
      dispatch(fetchUserFailure('No valid GHL token found'));
      return Promise.reject('No valid GHL token found');
    }

    return axios
      .get(`${NEW_API_URL}/api/locations`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })
      .then((response) => {
        dispatch(fetchUserSuccess(response.data.locations));
      })
      .catch((error) => {
        dispatch(fetchUserFailure(error.message));
      });
  };
};




export const getPipelines = () => {
  return (dispatch) => {
    dispatch(fetchUserRequest());

    return axios.get(`${base_url}/api/locations/`, {
      params: {
        companyId: "09ntIsRbx4gAxchbHqe7",
        offset: 0,
        pageLimit: 30,
        sort: [{ field: "name_sort", direction: "asc" }]
      }
    })
      .then(response => {
        const locations = response.data.locations;
        dispatch(fetchUserSuccess(locations));
      })
      .catch(error => {
        const errorMsg = error.message;
        dispatch(fetchUserFailure(errorMsg));
      });
  };
};