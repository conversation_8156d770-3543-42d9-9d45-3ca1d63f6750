const express = require('express');
const cors = require('cors');
require('dotenv').config();
const router = express.Router();

const app = express();

// Enable CORS for your React frontend
app.use(cors());

// Middleware to parse JSON
app.use(express.json());

// Route to get location API keys
router.get('/location-keys', async (req, res) => {
  try {
    // The API URL shown in your screenshot (corrected format)
    const apiUrl = 'https://backend.leadconnectorhq.com/oauth/keys/09ntIsRbx4gAxchbHqe7?all=true&limit=50&skip=0';
    
    // Your GHL authorization token
    const authToken = process.env.GHL_API_KEY;
    
    const response = await fetch(apiUrl, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching location API keys:', error.message);
    res.status(500).json({ 
      error: 'Failed to fetch location API keys',
      details: error.message 
    });
  }
});

module.exports = router;
