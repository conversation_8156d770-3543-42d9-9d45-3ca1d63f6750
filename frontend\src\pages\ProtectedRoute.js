import React from "react";
import { Navigate } from "react-router-dom";
import { isAuthenticated } from "./auth"; // Import from your auth utility

// Component to protect routes that require authentication
const ProtectedRoute = ({ children }) => {
  const authenticated = isAuthenticated();

  if (!authenticated) {
    // Redirect to sign-in page if not authenticated
    return <Navigate to="/sign-in" replace />;
  }

  return children;
};

export default ProtectedRoute;
