const express = require("express");
require("dotenv").config();

const router = express.Router();

const CLIENT_ID = process.env.GHL_CLIENT_ID || "67990c955eefdf2ff5493cfd-m70b87zi";
const CLIENT_SECRET = process.env.GHL_CLIENT_SECRET || "a80adb1c-bcd8-41ff-930c-aa4dd63c0fb2";
const REDIRECT_URI = process.env.GHL_REDIRECT_URI || "http://localhost:5000/api/auth/oauth/callback";
const FRONTEND_DASHBOARD_URL = process.env.FRONTEND_URL || "http://localhost:3000";

const SCOPES =
  "businesses.readonly businesses.write locations.write locations.readonly contacts.readonly contacts.write companies.readonly";

// Step 1: Redirect to GHL Authorization URL
router.get("/ghl", (req, res) => {
  const authUrl = `https://marketplace.gohighlevel.com/oauth/chooselocation?response_type=code&client_id=${CLIENT_ID}&redirect_uri=${REDIRECT_URI}&scope=${encodeURIComponent(SCOPES)}`;
  res.json({ authUrl });
});

// Step 2: Handle OAuth callback and exchange code for access token
router.get("/oauth/callback", async (req, res) => {
  const { code, state } = req.query; // state contains the user ID

  if (!code) {
    return res.status(400).json({ error: "Authorization code is missing" });
  }

  if (!state) {
    return res.status(400).json({ error: "User state is missing" });
  }

  try {
    const tokenResponse = await fetch("https://services.leadconnectorhq.com/oauth/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "authorization_code",
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        redirect_uri: REDIRECT_URI,
        code,
      }),
    });

    if (!tokenResponse.ok) {
      throw new Error(`HTTP error! Status: ${tokenResponse.status}`);
    }

    const data = await tokenResponse.json();
    const { access_token, refresh_token } = data;
    console.log("Access Token:", access_token);

    // Store GHL tokens in database associated with the user
    const userId = state; // User ID from state parameter

    try {
      // Check if GHL data already exists for this user
      const [existingRows] = await req.app.locals.db.execute(
        "SELECT * FROM ghl_data WHERE userId = ?",
        [userId]
      );

      if (existingRows.length > 0) {
        // Update existing record
        await req.app.locals.db.execute(
          `UPDATE ghl_data SET
            accessToken = ?, refreshToken = ?, expiresIn = ?, tokenType = ?,
            locationId = ?, companyId = ?, scope = ?, userType = ?, uniqueId = ?, rawResponse = ?
          WHERE userId = ?`,
          [
            data.access_token || null,
            data.refresh_token || null,
            data.expires_in || null,
            data.token_type || null,
            data.locationId || null,
            data.companyId || null,
            data.scope ? JSON.stringify(data.scope) : null,
            data.userType || null,
            data.uniqueId || null,
            JSON.stringify(data) || null,
            userId,
          ]
        );
      } else {
        // Insert new record
        await req.app.locals.db.execute(
          `INSERT INTO ghl_data (
            userId, accessToken, refreshToken, expiresIn, tokenType,
            locationId, companyId, scope, userType, uniqueId, rawResponse
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            userId,
            data.access_token || null,
            data.refresh_token || null,
            data.expires_in || null,
            data.token_type || null,
            data.locationId || null,
            data.companyId || null,
            data.scope ? JSON.stringify(data.scope) : null,
            data.userType || null,
            data.uniqueId || null,
            JSON.stringify(data) || null,
          ]
        );
      }

      console.log(`GHL tokens stored for user ${userId}`);
    } catch (dbError) {
      console.error("Database error:", dbError);
      // Continue with redirect even if DB storage fails
    }

    // Redirect to frontend with success message
    res.redirect(`${FRONTEND_DASHBOARD_URL}?ghl_auth=success`);
  } catch (error) {
    console.error("Error exchanging code for access token:", error.message);
    res.redirect(`${FRONTEND_DASHBOARD_URL}?ghl_auth=error`);
  }
});

router.post("/refresh", async (req, res) => {
  const { refresh_token } = req.body;

  if (!refresh_token) {
    return res.status(400).json({ error: "Refresh token is missing" });
  }

  try {
    const refreshResponse = await fetch("https://services.leadconnectorhq.com/oauth/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "refresh_token",
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        refresh_token,
      }),
    });

    if (!refreshResponse.ok) {
      throw new Error(`HTTP error! Status: ${refreshResponse.status}`);
    }

    const data = await refreshResponse.json();
    const { access_token, refresh_token: new_refresh_token } = data;

    console.log("New Access Token:", access_token);
    console.log("New Refresh Token:", new_refresh_token);

    res.json({
      access_token,
      refresh_token: new_refresh_token,
    });
  } catch (error) {
    console.error("Error refreshing token:", error.message);
    res.status(500).json({ error: "Failed to refresh token" });
  }
});

module.exports = router;
