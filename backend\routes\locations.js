const express = require('express');
const router = express.Router();

const GHL_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb21wYW55X2lkIjoiMDludElzUmJ4NGdBeGNoYkhxZTciLCJ2ZXJzaW9uIjoxLCJpYXQiOjE3Mzk3ODY0Mjc5MjAsInN1YiI6IlFScnA2WDE2RVRSNXNndWx0SWFxIn0.1j1oqgkqZA_5Db-ioQeWm0IMcoMOhL-j-YcOOmp0K2w";

// router.get('/locations', async (req, res) => {
//   try {
//     const response = await fetch("https://rest.gohighlevel.com/v1/locations", {
//       method: "GET",
//       headers: {
//         Authorization: `Bearer ${GHL_API_KEY}`,
//       },
//     });

//     if (!response.ok) {
//       throw new Error(`HTTP error! Status: ${response.status}`);
//     }

//     const data = await response.json();
//     res.json(data);
//   } catch (error) {
//     console.error("Error fetching locations:", error);
//     res.status(500).json({ error: "Failed to fetch location data" });
//   }
// });


router.get('/locations', async (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Missing Authorization header" });
  }

  try {
    const response = await fetch("https://services.leadconnectorhq.com/locations/search?limit=1000", {
      method: "GET",
      headers: {
        Authorization: authHeader,
        Version: "2021-07-28",
        Accept: "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    // console.log(data)
    res.json(data);
  } catch (error) {
    console.error("Error fetching locations:", error);
    res.status(500).json({ error: "Failed to fetch location data" });
  }
});

module.exports = router;
