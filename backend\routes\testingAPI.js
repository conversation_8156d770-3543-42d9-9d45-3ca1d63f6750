// routes/testingAPI.js
const express = require("express");
const router = express.Router();

// Test endpoint to generate API Key for location
router.get("/test-locations/:id", async (req, res) => {
  const AGENCY_API_KEY = process.env.AGENCY_API_KEY;
  const locationId = 'LwWxfqf87cX7XP2Wiqmc';
  
  try {
    const response = await fetch(`https://rest.gohighlevel.com/v1/locations/LwWxfqf87cX7XP2Wiqmc`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AGENCY_API_KEY}`,
      }
    });
    
    const data = await response.json();
    console.log('Sub-account API Key:', data.apiKey);
  } catch (error) {
    console.error('Error generating API Key:', error);
  }
});

module.exports = router;
