# Conversion Funnel Implementation

## Overview
This implementation provides a comprehensive conversion funnel tracking system that monitors the progression of leads through your sales pipeline, specifically tracking:

1. **Lead → Qualified Lead** - Conversion rate from total leads to qualified leads
2. **Lead → Appointment** - Conversion rate from total leads to booked appointments  
3. **Appointment No-Show Rate** - Percentage of appointments that result in no-shows

## Features

### Backend API (`backend/routes/funnelMetrics.js`)
- **GET `/api/conversion-funnel`** - Main endpoint for funnel metrics
- **GET `/api/timeline`** - Historical timeline data
- Fetches data from GoHighLevel API for contacts and appointments
- Calculates conversion rates and provides daily breakdowns
- Supports date range filtering and location-specific data

### Frontend Dashboard (`frontend/src/pages/Funnel.js`)
- Modern Material-UI based interface
- Interactive date range selection
- Location dropdown for multi-location businesses
- Visual funnel representation with color-coded metrics
- Daily breakdown cards showing detailed metrics
- Real-time data refresh capabilities

## API Endpoints

### Conversion Funnel Metrics
```
GET /api/conversion-funnel?locationId={id}&startDate={YYYY-MM-DD}&endDate={YYYY-MM-DD}
```

**Headers:**
```
Authorization: Bearer {your_token}
```

**Response:**
```json
{
  "dateRange": {
    "startDate": "2024-01-01",
    "endDate": "2024-01-31"
  },
  "overallFunnel": {
    "totalLeads": 150,
    "qualifiedLeads": 45,
    "appointments": 30,
    "noShows": 5,
    "conversionRates": {
      "leadToQualified": 30.00,
      "leadToAppointment": 20.00,
      "noShowRate": 16.67
    }
  },
  "dailyData": [
    {
      "date": "2024-01-01",
      "metrics": {
        "totalLeads": 5,
        "qualifiedLeads": 2,
        "appointments": 1,
        "noShows": 0,
        "conversionRates": {
          "leadToQualified": 40.00,
          "leadToAppointment": 20.00,
          "noShowRate": 0.00
        }
      }
    }
  ]
}
```

## How It Works

### Lead Qualification
- Contacts are considered "qualified" if they have associated opportunities
- The system checks the `opportunities` array in the contact data

### Appointment Tracking
- Appointments are fetched from the GHL appointments API
- No-shows are identified by status values: `"no_show"` or `"cancelled"`

### Conversion Rate Calculation
- **Lead to Qualified**: `(qualifiedLeads / totalLeads) * 100`
- **Lead to Appointment**: `(appointments / totalLeads) * 100`
- **No-Show Rate**: `(noShows / appointments) * 100`

## Setup Instructions

1. **Backend Setup**
   - Ensure the `funnelMetrics.js` route is properly registered in `server.js`
   - Verify GHL API credentials are configured in environment variables
   - Install required dependencies: `dayjs`, `express`

2. **Frontend Setup**
   - The funnel page is accessible at `/funnel`
   - Add the route to your navigation menu
   - Ensure Material-UI dependencies are installed

3. **Authentication**
   - The system uses Bearer token authentication
   - Tokens should be stored in localStorage as `accessToken`

## Usage

1. Navigate to the Conversion Funnel page
2. Select a location from the dropdown
3. Choose a date range for analysis
4. View the overall funnel summary and daily breakdown
5. Use the refresh button to update data

## Color Coding

### Conversion Rates
- **Green (Success)**: ≥70% conversion rate
- **Orange (Warning)**: 40-69% conversion rate  
- **Red (Error)**: <40% conversion rate

### No-Show Rates
- **Green (Success)**: ≤10% no-show rate
- **Orange (Warning)**: 11-25% no-show rate
- **Red (Error)**: >25% no-show rate

## Troubleshooting

### Common Issues
1. **No data displayed**: Check if the selected location has contacts/appointments
2. **Authentication errors**: Verify the access token is valid and not expired
3. **API errors**: Check GHL API credentials and network connectivity

### Debug Steps
1. Check browser console for error messages
2. Verify API endpoints are accessible
3. Confirm date range is valid
4. Ensure location ID is correct

## Future Enhancements

- Add export functionality for reports
- Implement trend analysis and forecasting
- Add custom funnel stage definitions
- Include revenue tracking per funnel stage
- Add comparison features between time periods
