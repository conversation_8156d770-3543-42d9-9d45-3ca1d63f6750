import React from 'react';
import { styled } from '@mui/material/styles';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';
import { Box, Button, Stack, Typography } from '@mui/material';

// Define color presets for different situations
const tooltipPresets = {
  info: {
    background: '#17a2b8',
    color: '#ffffff',
  },
  success: {
    background: '#28a745',
    color: '#ffffff',
  },
  warning: {
    background: '#ffc107',
    color: '#212529',
  },
  error: {
    background: '#dc3545',
    color: '#ffffff',
  },
  primary: {
    background: '#007bff',
    color: '#ffffff',
  },
  purple: {
    background: '#6f42c1',
    color: '#ffffff',
  },
  teal: {
    background: '#20c997',
    color: '#ffffff',
  },
  orange: {
    background: '#fd7e14',
    color: '#ffffff',
  },
  pink: {
    background: '#e83e8c',
    color: '#ffffff',
  },
  dark: {
    background: '#343a40',
    color: '#ffffff',
  }
};

// Create styled tooltip component
const ColorfulTooltip = styled(({ className, variant = 'info', ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme, variant }) => {
  const preset = tooltipPresets[variant] || tooltipPresets.info;
  
  return {
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: preset.background,
      color: preset.color,
      fontSize: '14px',
      fontWeight: 500,
      padding: '10px 16px',
      borderRadius: '8px',
      maxWidth: 220,
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
    [`& .${tooltipClasses.arrow}`]: {
      color: preset.background,
      '&::before': {
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      },
    },
  };
});

// Alternative: Rounded speech bubble style (like your image)
const SpeechBubbleTooltip = styled(({ className, variant = 'teal', ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme, variant }) => {
  const preset = tooltipPresets[variant] || tooltipPresets.teal;
  
  return {
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: preset.background,
      color: preset.color,
      fontSize: '14px',
      fontWeight: 500,
      padding: '12px 20px',
      borderRadius: '20px',
      maxWidth: 250,
      boxShadow: '0 6px 16px rgba(0, 0, 0, 0.2)',
      position: 'relative',
    },
    [`& .${tooltipClasses.arrow}`]: {
      color: preset.background,
      fontSize: 16,
      '&::before': {
        borderRadius: '2px',
        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.2)',
      },
    },
  };
});

// Usage component demonstrating all variants
export default function ColorfulTooltipDemo() {
  return (
    <Box sx={{ p: 4 }}>
      <Typography variant="h5" gutterBottom>
        Colorful Tooltip Presets
      </Typography>
      
      <Stack spacing={4}>
        {/* Standard Style Tooltips */}
        <Box>
          <Typography variant="h6" gutterBottom>
            Standard Style
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap" gap={2}>
            <ColorfulTooltip title="This is an info tooltip" variant="info" arrow>
              <Button variant="outlined">Info</Button>
            </ColorfulTooltip>
            
            <ColorfulTooltip title="Success! Operation completed" variant="success" arrow>
              <Button variant="outlined" color="success">Success</Button>
            </ColorfulTooltip>
            
            <ColorfulTooltip title="Warning: Check your input" variant="warning" arrow>
              <Button variant="outlined" color="warning">Warning</Button>
            </ColorfulTooltip>
            
            <ColorfulTooltip title="Error: Something went wrong" variant="error" arrow>
              <Button variant="outlined" color="error">Error</Button>
            </ColorfulTooltip>
            
            <ColorfulTooltip title="Primary action tooltip" variant="primary" arrow>
              <Button variant="outlined" color="primary">Primary</Button>
            </ColorfulTooltip>
            
            <ColorfulTooltip title="Purple themed tooltip" variant="purple" arrow>
              <Button variant="outlined">Purple</Button>
            </ColorfulTooltip>
          </Stack>
        </Box>

        {/* Speech Bubble Style Tooltips */}
        <Box>
          <Typography variant="h6" gutterBottom>
            Speech Bubble Style
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap" gap={2}>
            <SpeechBubbleTooltip title="Teal speech bubble" variant="teal" arrow placement="bottom">
              <Button variant="contained" sx={{ bgcolor: '#20c997' }}>Teal</Button>
            </SpeechBubbleTooltip>
            
            <SpeechBubbleTooltip title="Orange notification" variant="orange" arrow placement="bottom">
              <Button variant="contained" sx={{ bgcolor: '#fd7e14' }}>Orange</Button>
            </SpeechBubbleTooltip>
            
            <SpeechBubbleTooltip title="Pink message" variant="pink" arrow placement="bottom">
              <Button variant="contained" sx={{ bgcolor: '#e83e8c' }}>Pink</Button>
            </SpeechBubbleTooltip>
            
            <SpeechBubbleTooltip title="Dark mode tooltip" variant="dark" arrow placement="bottom">
              <Button variant="contained" sx={{ bgcolor: '#343a40' }}>Dark</Button>
            </SpeechBubbleTooltip>
          </Stack>
        </Box>
      </Stack>
    </Box>
  );
}

// Export the components for use in other files
export { ColorfulTooltip, SpeechBubbleTooltip, tooltipPresets };