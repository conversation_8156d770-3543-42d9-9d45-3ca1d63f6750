const express = require('express');
const router = express.Router();

// Your GHL API credentials
const GHL_API_KEY = process.env.GHL_CLIENT_SECRET;

// GHL API configuration
const GHL_BASE_URL = 'https://services.leadconnectorhq.com';
const GHL_API_VERSION = '2021-07-28';

// Middleware to validate GHL API requests
const validateGHLRequest = (req, res, next) => {
  if (!GHL_API_KEY) {
    return res.status(500).json({ 
      error: 'GHL API key not configured', 
      details: 'Please check your environment variables and ensure GHL_LOCATION_KEY is set correctly.'
    });
  }
  
  console.log(`API Key format check: ${GHL_API_KEY.substring(0, 3)}...${GHL_API_KEY.slice(-3)}`);
  next();
};

// Utility function for making fetch requests
const fetchGHL = async (url, options = {}) => {
  const response = await fetch(url, {
    headers: {
      'Authorization': `Bearer ${GHL_API_KEY}`,
      'Content-Type': 'application/json',
      'Version': GHL_API_VERSION,
      ...options.headers,
    },
    ...options
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw { status: response.status, data: errorData };
  }

  return response.json();
};

/**
 * GET - List all subscriptions
 */
router.get('/subscriptions', validateGHLRequest, async (req, res) => {
  try {
    console.log('Fetching subscriptions from GHL API...');
    const queryParams = new URLSearchParams(req.query).toString();
    const data = await fetchGHL(`${GHL_BASE_URL}/payments/subscriptions?${queryParams}`);
    res.status(200).json(data);
  } catch (error) {
    console.error('Error fetching GHL subscriptions:', error);
    res.status(error.status || 500).json({ error: error.data || 'Failed to fetch subscriptions' });
  }
});

/**
 * GET - Get subscription by ID
 */
router.get('/subscriptions/:subscriptionId', validateGHLRequest, async (req, res) => {
  try {
    const { subscriptionId } = req.params;
    const data = await fetchGHL(`${GHL_BASE_URL}/payments/subscriptions/${subscriptionId}`);
    res.status(200).json(data);
  } catch (error) {
    console.error('Error fetching GHL subscription:', error);
    res.status(error.status || 500).json({ error: error.data || 'Failed to fetch subscription' });
  }
});

/**
 * POST - Create a new subscription
 */
router.post('/subscriptions', validateGHLRequest, async (req, res) => {
  try {
    const data = await fetchGHL(`${GHL_BASE_URL}/payments/subscriptions`, {
      method: 'POST',
      body: JSON.stringify(req.body),
    });
    res.status(201).json(data);
  } catch (error) {
    console.error('Error creating GHL subscription:', error);
    res.status(error.status || 500).json({ error: error.data || 'Failed to create subscription' });
  }
});

/**
 * PUT - Update an existing subscription
 */
router.put('/subscriptions/:subscriptionId', validateGHLRequest, async (req, res) => {
  try {
    const { subscriptionId } = req.params;
    const data = await fetchGHL(`${GHL_BASE_URL}/payments/subscriptions/${subscriptionId}`, {
      method: 'PUT',
      body: JSON.stringify(req.body),
    });
    res.status(200).json(data);
  } catch (error) {
    console.error('Error updating GHL subscription:', error);
    res.status(error.status || 500).json({ error: error.data || 'Failed to update subscription' });
  }
});

/**
 * DELETE - Cancel a subscription
 */
router.delete('/subscriptions/:subscriptionId', validateGHLRequest, async (req, res) => {
  try {
    const { subscriptionId } = req.params;
    const data = await fetchGHL(`${GHL_BASE_URL}/payments/subscriptions/${subscriptionId}`, {
      method: 'DELETE',
    });
    res.status(200).json(data);
  } catch (error) {
    console.error('Error cancelling GHL subscription:', error);
    res.status(error.status || 500).json({ error: error.data || 'Failed to cancel subscription' });
  }
});

module.exports = router;
