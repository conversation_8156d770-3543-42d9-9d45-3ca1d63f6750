// config/ghl.js
const fetch = require("node-fetch");

const createFetchInstance = (baseURL) => {
  return async (endpoint, options = {}) => {
    const url = `${baseURL}${endpoint}`;

    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.GHL_API_KEY}`,
      Version: "2021-07-28",
      ...(options.headers || {}),
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    return response.json();
  };
};

const servicesAPI = createFetchInstance(process.env.GHL_SERVICES_URL);
const backendAPI = createFetchInstance(process.env.GHL_BACKEND_URL);

module.exports = { servicesAPI, backendAPI };
