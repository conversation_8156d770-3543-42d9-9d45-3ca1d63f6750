import React from "react";
import { useNavigate } from "react-router-dom";

const Auth = () => {

  const navigate = useNavigate();

  const handleGHLAuth = async () => {
   // Redirect to GHL auth page
    try {
      const response = await fetch("http://localhost:5000/api/auth/ghl");
      const { authUrl } = await response.json();
      
      window.location.href = authUrl; // Redirect to GHL auth page
    } catch (error) {
      console.error("Authentication error:", error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-6">Connect to GHL</h1>
        <button
          onClick={handleGHLAuth}
          className="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600"
        >
          Connect GHL Account
        </button>
      </div>
    </div>
  );
};

export default Auth;
