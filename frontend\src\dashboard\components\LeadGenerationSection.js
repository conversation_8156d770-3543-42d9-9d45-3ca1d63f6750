import { useState } from "react";
import {
  Box,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Paper,
  Chip,
  useTheme,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { TrendingUp, People, TrackChanges, Percent } from "@mui/icons-material";

const leadSources = [
  "All Sources",
  "Facebook Ads",
  "Google Ads",
  "Website",
  "Referral",
  "Cold Calling",
  "Email Marketing",
  "Social Media",
];

const mockLeadData = {
  "All Sources": {
    totalLeads: 1245,
    qualifiedLeads: 892,
    appointments: 534,
    conversionRate: 23.1,
  },
  "Facebook Ads": {
    totalLeads: 456,
    qualifiedLeads: 312,
    appointments: 198,
    conversionRate: 25.8,
  },
  "Google Ads": {
    totalLeads: 321,
    qualifiedLeads: 245,
    appointments: 156,
    conversionRate: 22.4,
  },
  Website: {
    totalLeads: 234,
    qualifiedLeads: 167,
    appointments: 89,
    conversionRate: 19.2,
  },
  Referral: {
    totalLeads: 156,
    qualifiedLeads: 134,
    appointments: 67,
    conversionRate: 28.9,
  },
  "Cold Calling": {
    totalLeads: 78,
    qualifiedLeads: 34,
    appointments: 24,
    conversionRate: 15.4,
  },
};

export function LeadGenerationSection() {
  const theme = useTheme();
  const [selectedSource, setSelectedSource] = useState("All Sources");
  
  // Calculate default 7-day range
  const getDefaultDates = () => {
    const today = new Date();
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 7);
    
    return {
      start: sevenDaysAgo,
      end: today
    };
  };
  
  const defaultDates = getDefaultDates();
  const [startDate, setStartDate] = useState(defaultDates.start);
  const [endDate, setEndDate] = useState(defaultDates.end);

  const leadData = mockLeadData[selectedSource] || mockLeadData["All Sources"];

  // Get today's date for max date validation
  const today = new Date();
  today.setHours(23, 59, 59, 999); // Set to end of day to include today

  const handleStartDateChange = (newValue) => {
    setStartDate(newValue);
    // If end date is before new start date, update end date
    if (endDate < newValue) {
      setEndDate(newValue);
    }
  };

  const handleEndDateChange = (newValue) => {
    setEndDate(newValue);
  };

  const MetricCard = ({ title, value, icon: Icon, change, color }) => (
    <Card elevation={2} sx={{ height: "100%", bgcolor: "background.paper" }}>
      <CardContent sx={{ p: 1 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ fontWeight: 500 }}
          >
            {title}
          </Typography>
          <Icon sx={{ fontSize: 20, color: color }} />
        </Box>
        <Typography variant="h4" fontWeight="bold" mb={1}>
          {value}
        </Typography>
        <Chip
          label={change}
          size="small"
          color="success"
          variant="outlined"
          sx={{ fontSize: "0.75rem" }}
        />
      </CardContent>
    </Card>
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Card
        sx={{
          backgroundColor: (theme) =>
            theme.palette.mode === "dark" ? theme.palette.grey[900] : "#ffffff",
          boxShadow:
            theme.palette.mode === "dark"
              ? "0 1px 3px rgba(0,0,0,0.5)"
              : "0 1px 3px rgba(0,0,0,0.12)",
          borderRadius: 2,
        }}
      >
        <CardContent>
          <Typography component="h2" variant="subtitle2" fontWeight="bold" mb={3}>
            Lead Generation Analytics
          </Typography>

          {/* Filters Row */}
          <Box display="flex" flexWrap="wrap" gap={3} mb={4}>
            <FormControl sx={{ minWidth: 200, flex: 1 }}>
              <InputLabel>Lead Source</InputLabel>
              <Select
                value={selectedSource}
                label="Lead Source"
                onChange={(e) => setSelectedSource(e.target.value)}
              >
                {leadSources.map((source) => (
                  <MenuItem key={source} value={source}>
                    {source}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <DatePicker
              label="From Date"
              value={startDate}
              onChange={handleStartDateChange}
              maxDate={today}
              slotProps={{ 
                textField: { 
                  sx: { minWidth: 180, flex: 1 },
                  helperText: "Select start date"
                } 
              }}
            />

            <DatePicker
              label="To Date"
              value={endDate}
              onChange={handleEndDateChange}
              minDate={startDate}
              maxDate={today}
              slotProps={{ 
                textField: { 
                  sx: { minWidth: 180, flex: 1 },
                  helperText: "Must be after start date"
                } 
              }}
            />
          </Box>

          {/* Metrics Cards */}
          <Grid container mt={3} spacing={3}>
            <Grid item size={{ xs: 12, sm: 6, lg: 3 }}>
              <MetricCard
                title="Total Leads Generated"
                value={leadData.totalLeads.toLocaleString()}
                icon={People}
                change="+12.5% from last period"
                color="#1976d2"
              />
            </Grid>
            <Grid item size={{ xs: 12, sm: 6, lg: 3 }}>
              <MetricCard
                title="Qualified Leads"
                value={leadData.qualifiedLeads.toLocaleString()}
                icon={TrackChanges}
                change="+8.3% from last period"
                color="#2e7d32"
              />
            </Grid>

            <Grid item size={{ xs: 12, sm: 6, lg: 3 }}>
              <MetricCard
                title="Appointments Scheduled"
                value={leadData.appointments.toLocaleString()}
                icon={TrendingUp}
                change="+15.2% from last period"
                color="#ed6c02"
              />
            </Grid>
            <Grid item size={{ xs: 12, sm: 6, lg: 3 }}>
              <MetricCard
                title="Conversion Rate"
                value={`${leadData.conversionRate}%`}
                icon={Percent}
                change="+2.1% from last period"
                color="#9c27b0"
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </LocalizationProvider>
  );
}