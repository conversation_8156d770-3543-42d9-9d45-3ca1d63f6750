// src/components/ContactsList.js
import React, { useState, useEffect } from 'react';
import { getContacts } from '../services/ghlService';

const ContactsList = () => {
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchContacts = async () => {
      try {
        setLoading(true);
        const data = await getContacts();
        setContacts(data.contacts || []);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch contacts');
        setLoading(false);
      }
    };

    fetchContacts();
  }, []);

  if (loading) return <div>Loading contacts...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="contacts-list">
      <h2>Contacts</h2>
      {contacts.length === 0 ? (
        <p>No contacts found</p>
      ) : (
        <ul>
          {contacts.map((contact) => (
            <li key={contact.id}>
              {contact.name} - {contact.email}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default ContactsList;