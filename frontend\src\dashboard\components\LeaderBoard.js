import React, { useState } from 'react';
import {
    Box,
    Typography,
    Card,
    CardContent,
    Avatar,
    Stack,
    Container,
    useTheme,
    useMediaQuery,
    Paper
} from '@mui/material';
import { Timeline, TrendingUp } from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';

const Leaderboard = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.down('md'));

    // Separate date states for start and end dates
    const [startDate, setStartDate] = useState(dayjs('2025-04-28'));
    const [endDate, setEndDate] = useState(dayjs('2025-05-28'));

    const salesData = [
        {
            id: 1,
            agency: 'Agency 4',
            totalSales: '$320,000',
            deals: 71,
            avgDeal: '$4,507',
            color: '#FFD700', // Gold
            bgColor: '#FFF9E6'
        },
        {
            id: 2,
            agency: 'Agency 2',
            totalSales: '$280,000',
            deals: 62,
            avgDeal: '$4,516',
            color: '#6B7280', // Silver/Gray
            bgColor: '#F8F9FA'
        },
        {
            id: 3,
            agency: 'Agency 5',
            totalSales: '$175,000',
            deals: 39,
            avgDeal: '$4,487',
            color: '#FF6B35', // Orange/Bronze
            bgColor: '#FFF4F1'
        },
        {
            id: 4,
            agency: 'Agency 1',
            totalSales: '$150,000',
            deals: 45,
            avgDeal: '$3,333',
            color: '#3B82F6', // Blue
            bgColor: '#F0F7FF'
        },
        {
            id: 5,
            agency: 'Agency 3',
            totalSales: '$95,000',
            deals: 28,
            avgDeal: '$3,393',
            color: '#3B82F6', // Blue
            bgColor: '#F0F7FF'
        }
    ];

    const handleStartDateChange = (newValue) => {
        setStartDate(newValue);
        // Add your data fetching logic here
        console.log('Start date changed:', newValue?.format('YYYY-MM-DD'));
    };

    const handleEndDateChange = (newValue) => {
        setEndDate(newValue);
        // Add your data fetching logic here
        console.log('End date changed:', newValue?.format('YYYY-MM-DD'));
    };


    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <Container maxWidth="lg" sx={{ py: 3 }}>
                {/* Header */}
                <Box sx={{ mb: 4 }}>
                    <Stack
                        direction={isMobile ? 'column' : 'row'}
                        alignItems={isMobile ? 'flex-start' : 'center'}
                        spacing={2}
                        sx={{ mb: 2 }}
                        justifyContent='space-between'
                    >
                        <Stack direction="row" alignItems="center" spacing={1}>
                            <Timeline sx={{ color: '#6B7280', fontSize: 20 }} />
                            <Typography
                                variant="h5"
                                component="h1"
                                sx={{
                                    fontWeight: 600,
                                    color: '#1F2937',
                                    fontSize: isMobile ? '1.25rem' : '1.5rem'
                                }}
                            >
                                Sales Performance Leaderboard
                            </Typography>
                        </Stack>

                        {/* Date Pickers */}
                        <Stack
                            direction={isMobile ? 'column' : 'row'}
                            spacing={2}
                            sx={{ ml: isMobile ? 0 : 'auto' }}
                        >
                            <DatePicker
                                label="Start Date"
                                value={startDate}
                                onChange={handleStartDateChange}
                                slotProps={{
                                    textField: {
                                        size: 'small',
                                        variant: 'outlined',
                                        sx: {
                                            '& .MuiOutlinedInput-root': {
                                                backgroundColor: 'white',
                                                fontSize: '0.875rem',
                                                minWidth: '140px',
                                                '& fieldset': {
                                                    borderColor: '#E5E7EB',
                                                },
                                                '&:hover fieldset': {
                                                    borderColor: '#9CA3AF',
                                                },
                                                '&.Mui-focused fieldset': {
                                                    borderColor: theme.palette.primary.main,
                                                },
                                            },
                                            '& .MuiInputAdornment-root .MuiButtonBase-root.MuiIconButton-root ': {
                                                border: 'none',
                                                backgroundColor: 'transparent !important'
                                            },
                                        },
                                    },
                                }}
                            />

                            <DatePicker
                                label="End Date"
                                value={endDate}
                                onChange={handleEndDateChange}
                                minDate={startDate} // Prevent end date before start date
                                slotProps={{
                                    textField: {
                                        size: 'small',
                                        variant: 'outlined',
                                        sx: {
                                            '& .MuiOutlinedInput-root': {
                                                backgroundColor: 'white',
                                                fontSize: '0.875rem',
                                                minWidth: '140px',
                                                '& fieldset': {
                                                    borderColor: '#E5E7EB',
                                                },
                                                '&:hover fieldset': {
                                                    borderColor: '#9CA3AF',
                                                },
                                                '&.Mui-focused fieldset': {
                                                    borderColor: theme.palette.primary.main,
                                                },

                                            },
                                            '& .MuiInputAdornment-root .MuiButtonBase-root.MuiIconButton-root ': {
                                                border: 'none',
                                                backgroundColor: 'transparent !important'
                                            },
                                        },
                                    },
                                }}
                            />
                        </Stack>
                    </Stack>

                </Box>
                {/* Leaderboard Cards */}
                <Stack spacing={2}>
                    {salesData.map((agency) => (
                        <Card
                            key={agency.id}
                            elevation={0}
                            sx={{
                                backgroundColor: agency.bgColor,
                                border: '1px solid',
                                borderColor: agency.id <= 3 ? agency.color + '20' : '#E5E7EB',
                                borderRadius: 2,
                                transition: 'all 0.2s ease-in-out',
                                py: 2.5,
                                '&:hover': {
                                    transform: 'translateY(-2px)',
                                    boxShadow: theme.shadows[1]
                                }
                            }}
                        >
                            {/* <CardContent sx={{ px: { xs: 2, sm: 3 }, py:2,'& .MuiCardContent-root ':'24px !important' }}> */}
                            <CardContent sx={{ px: { xs: 2, sm: 3 } }}>
                                <Stack
                                    direction="row"
                                    alignItems="center"
                                    spacing={2}
                                    flexWrap={isMobile ? 'wrap' : 'nowrap'}
                                >
                                    {/* Rank Badge */}
                                    <Avatar
                                        sx={{
                                            width: 32,
                                            height: 32,
                                            backgroundColor: agency.color,
                                            color: 'white',
                                            fontSize: '0.875rem',
                                            fontWeight: 600,
                                            flexShrink: 0
                                        }}
                                    >
                                        {agency.id}
                                    </Avatar>

                                    {/* Agency Name */}
                                    <Box sx={{ minWidth: 0, flex: isMobile ? '1 1 100%' : '0 0 auto' }}>
                                        <Typography
                                            variant="h6"
                                            sx={{
                                                fontWeight: 600,
                                                color: '#1F2937',
                                                fontSize: { xs: '1rem', sm: '1.125rem' },
                                                mb: isMobile ? 1 : 0
                                            }}
                                        >
                                            {agency.agency}
                                        </Typography>
                                    </Box>

                                    {/* Metrics */}
                                    <Stack
                                        direction={isMobile ? 'column' : 'row'}
                                        spacing={isMobile ? 1 : 4}
                                        sx={{
                                            ml: isMobile ? 0 : 'auto',
                                            width: isMobile ? '100%' : 'auto',
                                            alignItems: isMobile ? 'flex-start' : 'center'
                                        }}
                                    >
                                        {/* Total Sales */}
                                        <Stack direction="row" alignItems="center" spacing={1}>
                                            <Typography
                                                variant="body2"
                                                sx={{ color: '#6B7280', fontSize: '0.875rem' }}
                                            >
                                                💲 Total Sales:
                                            </Typography>
                                            <Typography
                                                variant="body1"
                                                sx={{
                                                    fontWeight: 600,
                                                    color: '#1F2937',
                                                    fontSize: '0.9rem'
                                                }}
                                            >
                                                {agency.totalSales}
                                            </Typography>
                                        </Stack>

                                        {/* Deals */}
                                        <Stack direction="row" alignItems="center" spacing={1}>
                                            <TrendingUp sx={{ color: '#6B7280', fontSize: 16 }} />
                                            <Typography
                                                variant="body2"
                                                sx={{ color: '#6B7280', fontSize: '0.875rem' }}
                                            >
                                                Deals:
                                            </Typography>
                                            <Typography
                                                variant="body1"
                                                sx={{
                                                    fontWeight: 600,
                                                    color: '#1F2937',
                                                    fontSize: '0.9rem'
                                                }}
                                            >
                                                {agency.deals}
                                            </Typography>
                                        </Stack>

                                        {/* Average Deal */}
                                        <Stack direction="row" alignItems="center" spacing={1}>
                                            <Typography
                                                variant="body2"
                                                sx={{ color: '#6B7280', fontSize: '0.875rem' }}
                                            >
                                                Avg Deal:
                                            </Typography>
                                            <Typography
                                                variant="body1"
                                                sx={{
                                                    fontWeight: 600,
                                                    color: '#1F2937',
                                                    fontSize: '0.9rem'
                                                }}
                                            >
                                                {agency.avgDeal}
                                            </Typography>
                                        </Stack>
                                    </Stack>
                                </Stack>
                            </CardContent>
                        </Card>
                    ))}
                </Stack>
            </Container>
        </LocalizationProvider>
    );
};

export default Leaderboard;