import { useState, useEffect } from 'react';
import axios from 'axios';


function PipelineList() {
  const [salesData, setSalesData] = useState([]);
  const [pipelineInfo, setPipelineInfo] = useState(null);
  const [monthlyRevenue, setMonthlyRevenue] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Pagination state
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10); // Default GHL page size
  const [total, setTotal] = useState(0); // From your screenshot

  useEffect(() => {
    fetchSalesData();
  }, [page, limit]); // Fetch data whenever page or limit changes

  const NEW_API_URL = process.env.REACT_APP_API_BASE_URL;

  const fetchSalesData = async () => {
    try {
      setLoading(true);
      
      // Call your Express backend endpoint
      const response = await axios.get(`${NEW_API_URL}/api/sales`, {
        params: { limit, page },
      });
      
      console.log('API Response:', response.data);
      
      // Update total if available in response
      if (response.data.total) {
        setTotal(response.data.total);
      }
      
      // Store pipeline info if available
      if (response.data.pipeline) {
        setPipelineInfo(response.data.pipeline);
      }
      
      // Get opportunities data
      const opportunities = response.data.opportunities || [];
      setSalesData(opportunities);
      
      // Get monthly revenue data
      setMonthlyRevenue(response.data.monthlyRevenue || []);
      
      setError(null);
    } catch (err) {
      console.error('Error fetching sales data:', err);
      setError('Failed to load sales data: ' + (err.response?.data?.details?.message || err.message));
    } finally {
      setLoading(false);
    }
  };

  const totalPages = Math.ceil(total / limit);
  
  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxDisplayedPages = 5;
    
    if (totalPages <= maxDisplayedPages) {
      // Display all pages if total pages are less than max displayed
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);
      
      // Calculate start and end based on current page
      let start = Math.max(2, page - 1);
      let end = Math.min(totalPages - 1, page + 1);
      
      // Adjust if we're near the beginning
      if (page <= 3) {
        end = Math.min(4, totalPages - 1);
      }
      
      // Adjust if we're near the end
      if (page >= totalPages - 2) {
        start = Math.max(2, totalPages - 3);
      }
      
      // Add ellipsis if needed at the beginning
      if (start > 2) {
        pages.push('...');
      }
      
      // Add middle pages
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      
      // Add ellipsis if needed at the end
      if (end < totalPages - 1) {
        pages.push('...');
      }
      
      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  };

  // Handle items per page change
  const handleLimitChange = (event) => {
    setLimit(parseInt(event.target.value));
    setPage(1); // Reset to first page when changing items per page
  };

  // Render loading state
  if (loading) return <div className="loading">Loading sales data...</div>;
  
  // Render error state
  if (error) return <div className="error">{error}</div>;

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };
  
  // Format month for display (YYYY-MM to Month YYYY)
  const formatMonth = (monthKey) => {
    const [year, month] = monthKey.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1, 1);
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  return (
    <div className="sales-container">
      <h2>Sales Pipeline</h2>
      
      {pipelineInfo && (
        <div className="pipeline-info">
          <h3>Pipeline: {pipelineInfo.name}</h3>
          
          <div className="pipeline-stages">
            <h4>Pipeline Stages:</h4>
            <ul>
              {pipelineInfo.stages.map(stage => (
                <li key={stage.id}>
                  {stage.name}
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}
      
      {/* Monthly Revenue Section */}
      {/* <div className="revenue-section">
        <h3>Monthly Revenue</h3>
        
        {monthlyRevenue?.length === 0 ? (
          <div className="no-data">
            <p>No revenue data available for the selected period.</p>
          </div>
        ) : (
          <>
            <div className="revenue-summary">
              <p>Total Revenue: {formatCurrency(
                monthlyRevenue?.reduce((sum, month) => sum + month.revenue, 0)
              )}</p>
              <p>Total Opportunities: {
                monthlyRevenue?.reduce((sum, month) => sum + month.count, 0)
              }</p>
            </div>
            
            <table className="revenue-table">
              <thead>
                <tr>
                  <th>Month</th>
                  <th>Revenue</th>
                  <th>Opportunities</th>
                  <th>Avg. Value</th>
                </tr>
              </thead>
              <tbody>
                {monthlyRevenue?.map((month) => (
                  <tr key={month.month}>
                    <td>{formatMonth(month.month)}</td>
                    <td>{formatCurrency(month.revenue)}</td>
                    <td>{month.count}</td>
                    <td>{formatCurrency(month.revenue / month.count)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          
            <div className="revenue-chart">
              <h4>Revenue Trend</h4>
              <div className="chart-container">
                {monthlyRevenue?.map(month => {
                  const maxRevenue = Math.max(...monthlyRevenue?.map(m => m.revenue));
                  const percentage = (month.revenue / maxRevenue) * 100;
                  
                  return (
                    <div className="chart-bar-container" key={month.month}>
                      <div className="chart-bar" style={{ height: `${percentage}%` }}>
                        <span className="bar-value">{formatCurrency(month.revenue)}</span>
                      </div>
                      <div className="bar-label">{month.month.split('-')[1]}/{month.month.split('-')[0].slice(2)}</div>
                    </div>
                  );
                })}
              </div>
            </div>
          </>
        )}
      </div> */}

      {/* Opportunities Section with Enhanced Pagination */}
      <div className="opportunities-section">
        <div className="opp-header">
          <h3>Opportunities</h3>
          <div className="opp-count">{total} opportunities</div>
          
          <div className="view-controls">
            <button className="view-button active">List</button>
            <button className="view-button">Board</button>
          </div>
        </div>
        
        <div className="filter-controls">
          <div className="filter-left">
            <select className="filter-select">
              <option>Pipeline</option>
            </select>
            <button className="filter-button">
              <i className="filter-icon">⚙️</i> Advanced Filters
            </button>
          </div>
          
          <div className="filter-right">
            <div className="search-box">
              <input type="text" placeholder="Search Opportunities" />
              <i className="search-icon">🔍</i>
            </div>
          </div>
        </div>
        
        {salesData.length === 0 ? (
          <div className="no-data">
            <h3>No Opportunities Found</h3>
            <p>There are currently no opportunities in this pipeline.</p>
          </div>
        ) : (
          <>
            <div className="summary">
              <h3>Opportunities Summary</h3>
              <p>Total Opportunities: {total}</p>
              <p>Total Value: {formatCurrency(
                salesData.reduce((sum, sale) => sum + (Number(sale.monetary_value || sale.value || 0) || 0), 0)
              )}</p>
            </div>
            
            <table>
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Value</th>
                  <th>Status</th>
                  <th>Stage</th>
                  <th>Created Date</th>
                </tr>
              </thead>
              <tbody>
                {salesData.map((sale) => (
                  <tr key={sale.id}>
                    <td>{sale.name || sale.title || 'Untitled'}</td>
                    <td>{formatCurrency(sale.monetary_value || sale.value || 0)}</td>
                    <td>
                      <span className={`status status-${(sale.status || '').toLowerCase().replace(/\s+/g, '-')}`}>
                        {sale.status || 'Unknown'}
                      </span>
                    </td>
                    <td>{sale.stage || sale.stageName || '-'}</td>
                    <td>{sale.created_at || sale.createdAt 
                      ? new Date(sale.created_at || sale.createdAt).toLocaleDateString() 
                      : 'N/A'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {/* Enhanced Pagination Controls - GHL Style */}
            <div className="pagination-container">
              <div className="pagination-info">
                Showing {(page - 1) * limit + 1} to {Math.min(page * limit, total)} of {total} opportunities
              </div>
              
              <div className="pagination-controls">
                <div className="per-page-selector">
                  <select value={limit} onChange={handleLimitChange} className="per-page-select">
                    <option value="25">25 per page</option>
                    <option value="50">50 per page</option>
                    <option value="100">100 per page</option>
                    <option value="200">200 per page</option>
                  </select>
                </div>
                
                <div className="pagination-buttons">
                  <button 
                    onClick={() => handlePageChange(page - 1)} 
                    disabled={page === 1}
                    className="pagination-button prev"
                  >
                    <i className="arrow-icon">←</i>
                  </button>
                  
                  <div className="page-numbers">
                    {getPageNumbers().map((pageNum, index) => (
                      typeof pageNum === 'number' ? (
                        <button 
                          key={index}
                          onClick={() => handlePageChange(pageNum)}
                          className={`page-number ${page === pageNum ? 'active' : ''}`}
                        >
                          {pageNum}
                        </button>
                      ) : (
                        <span key={index} className="page-ellipsis">{pageNum}</span>
                      )
                    ))}
                  </div>
                  
                  <button 
                    onClick={() => handlePageChange(page + 1)} 
                    disabled={page >= totalPages}
                    className="pagination-button next"
                  >
                    <i className="arrow-icon">→</i>
                  </button>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

// Add CSS styles
// const styles = `
// .sales-container {
//   font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
//   max-width: 1200px;
//   margin: 0 auto;
//   padding: 20px;
//   color: #333;
// }

// .sales-container h2 {
//   color: #2c3e50;
//   margin-bottom: 20px;
//   border-bottom: 2px solid #eee;
//   padding-bottom: 10px;
// }

// .pipeline-info {
//   background-color: #f8f9fa;
//   padding: 20px;
//   border-radius: 8px;
//   margin-bottom: 25px;
//   box-shadow: 0 2px 4px rgba(0,0,0,0.05);
// }

// .pipeline-info h3 {
//   color: #3498db;
//   margin-top: 0;
// }

// .pipeline-stages {
//   margin-top: 15px;
// }

// .pipeline-stages h4 {
//   margin-bottom: 10px;
//   color: #555;
// }

// .revenue-section {
//   margin: 30px 0;
//   background-color: #f8f9fa;
//   padding: 20px;
//   border-radius: 8px;
//   box-shadow: 0 2px 4px rgba(0,0,0,0.05);
// }

// .revenue-section h3 {
//   color: #2c3e50;
//   margin-top: 0;
//   border-bottom: 1px solid #dee2e6;
//   padding-bottom: 10px;
// }

// .revenue-table {
//   width: 100%;
//   border-collapse: collapse;
//   margin-top: 20px;
// }

// .revenue-table th, .revenue-table td {
//   padding: 12px 15px;
//   text-align: left;
//   border-bottom: 1px solid #dee2e6;
// }

// .revenue-table th {
//   background-color: #e9ecef;
//   font-weight: bold;
// }

// .chart-bar-container {
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   width: 70px;
// }

// .revenue-table tr:hover {
//   background-color: #f5f5f5;
// }

// .chart-container {
//   display: flex;
//   align-items: flex-end;
//   justify-content: space-around;
//   height: 200px;
//   margin: 20px 0 30px;
//   padding-bottom: 40px;
//   border-bottom: 1px solid #dee2e6;
// }

// .chart-bar {
//   width: 40px;
//   background-color: #4e73df;
//   border-radius: 3px 3px 0 0;
//   position: relative;
//   min-height: 5px;
//   transition: height 0.5s;
// }

// .bar-value {
//   position: absolute;
//   top: -25px;
//   left: 50%;
//   transform: translateX(-50%);
//   font-size: 0.8em;
//   white-space: nowrap;
// }

// .bar-label {
//   margin-top: 8px;
//   font-size: 0.8em;
//   text-align: center;
//   white-space: nowrap;
// }

// .pipeline-stages ul {
//   display: flex;
//   flex-wrap: wrap;
//   gap: 10px;
//   list-style: none;
//   padding: 0;
// }

// .pipeline-stages li {
//   background-color: #e9ecef;
//   padding: 8px 12px;
//   border-radius: 4px;
//   color: #495057;
//   font-size: 0.9em;
// }

// .summary {
//   background-color: #edf7ed;
//   padding: 15px;
//   border-radius: 8px;
//   margin-bottom: 20px;
//   box-shadow: 0 2px 4px rgba(0,0,0,0.05);
// }

// .summary h3 {
//   color: #2e7d32;
//   margin-top: 0;
// }

// table {
//   width: 100%;
//   border-collapse: collapse;
//   margin-top: 20px;
//   box-shadow: 0 2px 4px rgba(0,0,0,0.05);
// }

// table th, table td {
//   padding: 12px 15px;
//   text-align: left;
//   border-bottom: 1px solid #ddd;
// }

// table th {
//   background-color: #f2f2f2;
//   font-weight: bold;
// }

// table tr:hover {
//   background-color: #f5f5f5;
// }

// .status {
//   display: inline-block;
//   padding: 4px 8px;
//   border-radius: 4px;
//   font-size: 0.8em;
//   text-transform: capitalize;
// }

// .status-won {
//   background-color: #d4edda;
//   color: #155724;
// }

// .status-lost {
//   background-color: #f8d7da;
//   color: #721c24;
// }

// .status-active, .status-open {
//   background-color: #cce5ff;
//   color: #004085;
// }

// .status-in-progress {
//   background-color: #fff3cd;
//   color: #856404;
// }

// .loading {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   height: 200px;
//   font-size: 1.2em;
//   color: #666;
// }

// .error {
//   background-color: #f8d7da;
//   color: #721c24;
//   padding: 15px;
//   border-radius: 8px;
//   margin: 20px 0;
// }

// .no-data {
//   text-align: center;
//   padding: 40px 20px;
//   background-color: #f8f9fa;
//   border-radius: 8px;
//   color: #6c757d;
//   margin: 20px 0;
// }

// .no-data h3 {
//   color: #495057;
//   margin-bottom: 15px;
// }

// /* New GHL Style Pagination Styles */
// .opportunities-section {
//   margin-top: 30px;
// }

// .opp-header {
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   border-bottom: 1px solid #dee2e6;
//   padding-bottom: 15px;
//   margin-bottom: 15px;
// }

// .opp-header h3 {
//   font-size: 1.25rem;
//   margin: 0;
//   color: #333;
// }

// .opp-count {
//   color: #6c757d;
//   font-size: 0.9rem;
// }

// .view-controls {
//   display: flex;
// }

// .view-button {
//   padding: 6px 15px;
//   background: none;
//   border: 1px solid #dee2e6;
//   color: #6c757d;
//   font-size: 0.9rem;
//   cursor: pointer;
//   transition: all 0.2s;
// }

// .view-button:first-child {
//   border-radius: 4px 0 0 4px;
// }

// .view-button:last-child {
//   border-radius: 0 4px 4px 0;
// }

// .view-button.active {
//   background-color: #f8f9fa;
//   color: #495057;
//   border-color: #ced4da;
// }

// .filter-controls {
//   display: flex;
//   justify-content: space-between;
//   margin-bottom: 20px;
// }

// .filter-left {
//   display: flex;
//   gap: 10px;
// }

// .filter-select {
//   padding: 6px 10px;
//   border: 1px solid #ced4da;
//   border-radius: 4px;
//   font-size: 0.9rem;
// }

// .filter-button {
//   display: flex;
//   align-items: center;
//   gap: 5px;
//   padding: 6px 12px;
//   background-color: #fff;
//   border: 1px solid #ced4da;
//   border-radius: 4px;
//   font-size: 0.9rem;
//   cursor: pointer;
// }

// .search-box {
//   position: relative;
// }

// .search-box input {
//   padding: 6px 30px 6px 10px;
//   border: 1px solid #ced4da;
//   border-radius: 4px;
//   font-size: 0.9rem;
//   width: 200px;
// }

// .search-icon {
//   position: absolute;
//   right: 8px;
//   top: 50%;
//   transform: translateY(-50%);
//   color: #6c757d;
// }

// /* Enhanced Pagination Styles */
// .pagination-container {
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   padding: 20px 0;
//   border-top: 1px solid #dee2e6;
//   margin-top: 20px;
// }

// .pagination-info {
//   color: #6c757d;
//   font-size: 0.9rem;
//   margin-bottom: 15px;
// }

// .pagination-controls {
//   display: flex;
//   align-items: center;
//   gap: 20px;
// }

// .per-page-selector {
//   display: flex;
//   align-items: center;
// }

// .per-page-select {
//   padding: 6px 10px;
//   border: 1px solid #ced4da;
//   border-radius: 4px;
//   font-size: 0.9rem;
//   background-color: white;
// }

// .pagination-buttons {
//   display: flex;
//   align-items: center;
// }

// .pagination-button {
//   width: 36px;
//   height: 36px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   background-color: white;
//   border: 1px solid #dee2e6;
//   color: #495057;
//   cursor: pointer;
//   transition: all 0.2s;
// }

// .pagination-button.prev {
//   border-radius: 4px 0 0 4px;
// }

// .pagination-button.next {
//   border-radius: 0 4px 4px 0;
// }

// .pagination-button:disabled {
//   opacity: 0.5;
//   cursor: not-allowed;
// }

// .pagination-button:hover:not(:disabled) {
//   background-color: #f8f9fa;
// }

// .page-numbers {
//   display: flex;
//   align-items: center;
// }

// .page-number {
//   width: 36px;
//   height: 36px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   background-color: white;
//   border: 1px solid #dee2e6;
//   border-right: none;
//   color: #495057;
//   font-size: 0.9rem;
//   cursor: pointer;
//   transition: all 0.2s;
// }

// .page-number.active {
//   background-color: #0d6efd;
//   color: white;
//   border-color: #0d6efd;
//   z-index: 1;
// }

// .page-number:hover:not(.active) {
//   background-color: #f8f9fa;
// }

// .page-ellipsis {
//   width: 36px;
//   height: 36px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   background-color: white;
//   border: 1px solid #dee2e6;
//   border-right: none;
//   color: #6c757d;
//   font-size: 0.9rem;
// }

// /* Make sure the last item has a right border */
// .page-number:last-of-type,
// .page-ellipsis:last-of-type {
//   border-right: 1px solid #dee2e6;
// }
// `;

// Inject the styles
const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
// styleSheet.innerText = styles;
document.head.appendChild(styleSheet);

export default PipelineList;