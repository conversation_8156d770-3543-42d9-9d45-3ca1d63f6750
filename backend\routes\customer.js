const express = require('express');
const app = express();
const router = express.Router();

// Your GHL API credentials
const GHL_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6InQ1cjFZQlpreERDV1VPMkZ5RGtOIiwiY29tcGFueV9pZCI6IjA5bnRJc1JieDRnQXhjaGJIcWU3IiwidmVyc2lvbiI6MSwiaWF0IjoxNjg1NzI3MjA5NjYxLCJzdWIiOiJ1c2VyX2lkIn0.-UcbUyP-4nxElkvgSfm8s_78bk0cWb3A44B_vZj-29s";
const locationId = "t5r1YBZkxDCWUO2FyDkN";

// Endpoint to get total customers
router.get('/total-customers', async (req, res) => {
    try {
        const url = `https://rest.gohighlevel.com/v1/pipelines/kMJU2YtgfxG41dgErII8/opportunities?limit=1&status=customer`;

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${GHL_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        const totalCustomers = data.meta?.total || 0;

        res.json({
            success: true,
            total_customers: totalCustomers
        });
    } catch (error) {
        console.error('Error fetching customer count:', error.message);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve customer count',
            error: error.message
        });
    }
});

module.exports = router;
