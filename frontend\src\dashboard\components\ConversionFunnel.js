import React, { useState } from 'react';
import axios from 'axios';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Paper,
  Chip,
  TextField,
  useTheme
} from '@mui/material';
import Grid from "@mui/material/Grid2";
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { useParams } from 'react-router-dom';

const API_BASE_URL = 'http://localhost:5000/api';

const ConversionFunnel = () => {
  const theme = useTheme();
  const { locationId } = useParams();
  const [funnelData, setFunnelData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [startDate, setStartDate] = useState(dayjs().subtract(30, 'day'));
  const [endDate, setEndDate] = useState(dayjs());
  const [dateError, setDateError] = useState('');

  // Validate date range
  const validateDates = (start, end) => {
    if (!start || !end) return 'Both dates are required.';
    if (start.isAfter(end)) return 'Start date cannot be after end date.';
    if (end.isBefore(start)) return 'End date cannot be before start date.';
    if (start.isAfter(dayjs())) return 'Start date cannot be in the future.';
    if (end.isAfter(dayjs())) return 'End date cannot be in the future.';
    return '';
  };

  // Fetch conversion funnel data
  const fetchFunnelData = async () => {
    console.log('fetchFunnelData');

    const validationMsg = validateDates(startDate, endDate);
    setDateError(validationMsg);
    if (validationMsg) return;

    if (!locationId) {
      setError('No location selected in the URL.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('locationAccessTokenContact');
      console.log('token', token);

      if (!token) {
        setError('No authentication token found. Please log in again.');
        console.log('no token');
        return;
      }

      const response = await axios.post(
        `${API_BASE_URL}/conversion-funnel`,
        {
          locationId,
          startDate: startDate.format('YYYY-MM-DD'),
          endDate: endDate.format('YYYY-MM-DD')
        },
        {
          headers: { Authorization: `Bearer ${token}` }
        }
      );

      setFunnelData(response.data);
    } catch (err) {
      console.error('Error fetching funnel data:', err);
      if (err.response?.status === 401) {
        setError('Authentication failed. Please log in again.');
      } else if (err.response?.status === 404) {
        setError('No data found for the selected location and date range.');
      } else {
        setError(err.response?.data?.error || err.message || 'Failed to fetch funnel data');
      }
    } finally {
      setLoading(false);
    }
  };

  const formatPercentage = (value) => {
    return `${value.toFixed(2)}%`;
  };

  const getConversionColor = (rate) => {
    if (rate >= 70) return 'success';
    if (rate >= 40) return 'warning';
    return 'error';
  };

  const getNoShowColor = (rate) => {
    if (rate <= 10) return 'success';
    if (rate <= 25) return 'warning';
    return 'error';
  };

  if (!locationId) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">No location selected in the URL.</Alert>
      </Box>
    );
  }

  if (!funnelData && !loading) {
    return (
      <Card
        sx={{
          backgroundColor: (theme) =>
            theme.palette.mode === "dark" ? theme.palette.grey[900] : "#ffffff",
          boxShadow:
            theme.palette.mode === "dark"
              ? "0 1px 3px rgba(0,0,0,0.5)"
              : "0 1px 3px rgba(0,0,0,0.12)",
          borderRadius: 2,
        }}
      >
        <CardContent>
          <Typography variant="h4" gutterBottom>
            Conversion Funnel
          </Typography>

          <Paper sx={{ p: 2, mb: 3 }}>
            <Grid container spacing={2} alignItems="center">
              {/* <Grid item xs={12} sm={4}> */}
              <Grid size={{ xs: 12, sm: 4 }}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    label="Start Date"
                    value={startDate}
                    maxDate={dayjs()}
                    onChange={(date) => {
                      setStartDate(date);
                      setDateError(validateDates(date, endDate));
                    }}
                    renderInput={(params) => <TextField {...params} fullWidth error={!!dateError} />}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid size={{ xs: 12, sm: 4 }}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    label="End Date"
                    value={endDate}
                    minDate={startDate}
                    maxDate={dayjs()}
                    onChange={(date) => {
                      setEndDate(date);
                      setDateError(validateDates(startDate, date));
                    }}
                    renderInput={(params) => <TextField {...params} fullWidth error={!!dateError} />}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid size={{ xs: 12, sm: 4 }}>
                <Button
                  variant={
                    loading ||
                      !dateError
                      ? "contained"
                      : "outlined"
                  }
                  onClick={fetchFunnelData}
                  disabled={loading || !!dateError}
                  fullWidth
                >
                  {loading ? 'Loading...' : 'Submit'}
                </Button>
              </Grid>
            </Grid>
            {dateError && (
              <Alert severity="error" sx={{ mt: 2 }}>{dateError}</Alert>
            )}
          </Paper>
        </CardContent>
      </Card>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Card
        sx={{
          backgroundColor: (theme) =>
            theme.palette.mode === "dark" ? theme.palette.grey[900] : "#ffffff",
          boxShadow:
            theme.palette.mode === "dark"
              ? "0 1px 3px rgba(0,0,0,0.5)"
              : "0 1px 3px rgba(0,0,0,0.12)",
          borderRadius: 2,
        }}
      >
        <CardContent>
          <Typography variant="h4" gutterBottom>
            Conversion Funnel
          </Typography>
          <Paper sx={{ p: 2, mb: 3 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid size={{ xs: 12, sm: 4 }}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    label="Start Date"
                    value={startDate}
                    maxDate={dayjs()}
                    onChange={(date) => {
                      setStartDate(date);
                      setDateError(validateDates(date, endDate));
                    }}
                    renderInput={(params) => <TextField {...params} fullWidth error={!!dateError} />}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid size={{ xs: 12, sm: 4 }}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    label="End Date"
                    value={endDate}
                    minDate={startDate}
                    maxDate={dayjs()}
                    onChange={(date) => {
                      setEndDate(date);
                      setDateError(validateDates(startDate, date));
                    }}
                    renderInput={(params) => <TextField {...params} fullWidth error={!!dateError} />}
                  />
                </LocalizationProvider>
              </Grid>
              <Grid size={{ xs: 12, sm: 4 }}>
                {/* <Button
                  variant={
                    loading ||
                      !dateError
                      ? "contained"
                      : "outlined"
                  }
                  onClick={fetchFunnelData}
                  disabled={loading || !!dateError}
                  fullWidth
                >
                  {loading ? 'Loading...' : 'Submit'}
                </Button> */}
                <Button
                  variant={
                    loading
                      ? "outlined"
                      : "contained"
                  }
                  onClick={fetchFunnelData}
                  disabled={
                    loading
                  }
                  sx={{
                    minWidth: 100,
                    height: 56,
                    alignSelf: "flex-start",
                    cursor:
                      loading
                        ? "not-allowed"
                        : "pointer",
                  }}
                >
                  Submit
                </Button>
              </Grid>
            </Grid>
            {dateError && (
              <Alert severity="error" sx={{ mt: 2 }}>{dateError}</Alert>
            )}
          </Paper>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}
          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          )}
          {funnelData && (
            <>
              {/* Overall Funnel Summary */}
              <Typography variant="h5" gutterBottom sx={{ mt: 3 }}>
                Overall Funnel Summary
              </Typography>
              <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid size={{ xs: 12, md: 6 }}>
                  <Card>
                    <CardContent>
                      <Typography color="textSecondary" gutterBottom>
                        Total Leads
                      </Typography>
                      <Typography variant="h4">
                        {funnelData.totalLeads}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid size={{ xs: 12, md: 6 }}>
                  <Card>
                    <CardContent>
                      <Typography color="textSecondary" gutterBottom>
                        Qualified Leads
                      </Typography>
                      <Typography variant="h4">
                        {funnelData.qualifiedLeads}
                      </Typography>
                      <Chip
                        label={formatPercentage(funnelData.leadToQualifiedRate)}
                        color={getConversionColor(funnelData.leadToQualifiedRate)}
                        size="small"
                        sx={{ mt: 1 }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
              {/* Conversion Funnel Visualization */}
              <Typography variant="h5" gutterBottom>
                Conversion Funnel
              </Typography>
              <Paper sx={{ p: 3, mb: 4 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  {/* Lead to Qualified Lead */}
                  <Box sx={{
                    width: '80%',
                    maxWidth: 400,
                    bgcolor: 'primary.main',
                    color: 'white',
                    p: 2,
                    borderRadius: 2,
                    textAlign: 'center',
                    mb: 1
                  }}>
                    <Typography variant="h6">Leads</Typography>
                    <Typography variant="h4">{funnelData.totalLeads}</Typography>
                  </Box>
                  <Box sx={{ mb: 1 }}>↓</Box>
                  {/* Qualified Leads */}
                  <Box sx={{
                    width: '60%',
                    maxWidth: 300,
                    bgcolor: 'success.main',
                    color: 'white',
                    p: 2,
                    borderRadius: 2,
                    textAlign: 'center',
                    mb: 1
                  }}>
                    <Typography variant="h6">Qualified Leads</Typography>
                    <Typography variant="h4">{funnelData.qualifiedLeads}</Typography>
                    <Typography variant="body2">
                      {formatPercentage(funnelData.leadToQualifiedRate)} conversion
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </>
          )}
        </CardContent>
      </Card>
    </LocalizationProvider>
  );
};

export default ConversionFunnel;