// import React, { useRef, useEffect, useState } from "react";
// import { format } from "date-fns";
// import Box from "@mui/material/Box";
// import {
//   Typography,
//   Card,
//   Chip,
//   Checkbox,
//   Tooltip,
//   Avatar,
//   Badge,
//   RadioGroup,
//   FormControlLabel,
//   Radio,
//   Accordion,
//   AccordionSummary,
//   AccordionDetails,
//   Tabs,
//   Tab,
//   IconButton,
//   Stack,
//   Button,
//   Divider,
// } from "@mui/material";
// import { locationservice } from "../services/locationservice";
// import CloseIcon from "@mui/icons-material/Close";
// import ChevronRightIcon from "@mui/icons-material/ChevronRight";
// import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
// import FilterAltOutlinedIcon from "@mui/icons-material/FilterAltOutlined";
// import {
//   mockFields,
//   menuOptions,
//   sortOptions,
//   allFieldValues,
// } from "../contant/mockData";
// import SwapVertOutlinedIcon from "@mui/icons-material/SwapVertOutlined";
// import BackspaceOutlinedIcon from "@mui/icons-material/BackspaceOutlined";
// import SearchOutlinedIcon from "@mui/icons-material/SearchOutlined";
// import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
// import CallOutlinedIcon from "@mui/icons-material/CallOutlined";
// import SmsOutlinedIcon from "@mui/icons-material/SmsOutlined";
// import LocalOfferOutlinedIcon from "@mui/icons-material/LocalOfferOutlined";
// import DescriptionOutlinedIcon from "@mui/icons-material/DescriptionOutlined";
// import CheckBoxOutlinedIcon from "@mui/icons-material/CheckBoxOutlined";
// import CalendarMonthOutlinedIcon from "@mui/icons-material/CalendarMonthOutlined";
// import ExpandMoreOutlinedIcon from "@mui/icons-material/ExpandMoreOutlined";
// import DragIndicatorOutlinedIcon from "@mui/icons-material/DragIndicatorOutlined";

// import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

// const DragHandle = DragIndicatorOutlinedIcon;

// export default function ManageFieldsDrawer({ onClose }) {
//   const [manageFields, setManageFieldsOpen] = React.useState(false);
//   const toggleManageFieldsDrawer = (newOpen) => () =>
//     setManageFieldsOpen(newOpen);

//   const [layout, setLayout] = React.useState("default");

//   const handleLayoutChange = (_, newLayout) => {
//     if (newLayout !== null) setLayout(newLayout);
//   };

//   const MAX_FIELDS = 7;

//   const [selectedFields, setSelectedFields] = React.useState([
//     "Opportunity Owner",
//     "Business Name",
//     "Opportunity Source",
//     "Opportunity Value",
//     "Lost Reason",
//   ]);

//   const currentDateTime = format(new Date(), "MMM do, h:mm a");
//   const lockedField = "Opportunity Name";

//   const handleToggle = (field) => {
//     // if (field === lockedField) return;

//     setSelectedFields((prev) => {
//       if (prev.includes(field)) {
//         return prev.filter((f) => f !== field);
//       } else if (prev.length < MAX_FIELDS) {
//         return [...prev, field];
//       }
//       return prev;
//     });
//   };

//   const renderInfoItems = () => {
//     const result = [];

//     allFieldValues.forEach((groupObj) => {
//       Object.entries(groupObj).forEach(([groupName, fields]) => {
//         fields.forEach(({ label, value }) => {
//           if (selectedFields.includes(label)) {
//             result.push({ label, value });
//           }
//         });
//       });
//     });

//     return result;
//   };

//   const infoItems = renderInfoItems();

//   const isFieldSelected = (label) => selectedFields.includes(label);

//   const isFieldDisabled = (label) =>
//     !isFieldSelected(label) && selectedFields.length >= MAX_FIELDS;

//   const [tabValue, setTabValue] = React.useState(0);

//   const handleTabChange = (event, newValue) => {
//     setTabValue(newValue);
//   };

//   const iconData = [
//     {
//       key: "call",
//       label: "Call",
//       icon: <CallOutlinedIcon sx={{ width: 16, height: 16 }} />,
//     },
//     {
//       key: "chat",
//       label: "Unread Conversations",
//       icon: <SmsOutlinedIcon sx={{ width: 16, height: 16 }} />,
//     },
//     {
//       key: "tag",
//       label: "Tags",
//       icon: <LocalOfferOutlinedIcon sx={{ width: 16, height: 16 }} />,
//     },
//     {
//       key: "notes",
//       label: "Notes",
//       icon: <DescriptionOutlinedIcon sx={{ width: 16, height: 16 }} />,
//     },
//     {
//       key: "tasks",
//       label: "Tasks",
//       icon: <CheckBoxOutlinedIcon sx={{ width: 16, height: 16 }} />,
//     },
//     {
//       key: "appointments",
//       label: "Upcoming Confirmed Appointments",
//       icon: <CalendarMonthOutlinedIcon sx={{ width: 16, height: 16 }} />,
//     },
//   ];
//   // Initial list
//   const [icons, setIcons] = React.useState(
//     iconData.map((item) => ({ ...item, enabled: true, count: 1 }))
//   );

//   const handleIconToggle = (key) => {
//     setIcons((prev) =>
//       prev.map((item) =>
//         item.key === key ? { ...item, enabled: !item.enabled } : item
//       )
//     );
//   };

//   const handleDragEnd = (result) => {
//     if (!result.destination) return;
//     const reordered = Array.from(icons);
//     const [moved] = reordered.splice(result.source.index, 1);
//     reordered.splice(result.destination.index, 0, moved);
//     setIcons(reordered);
//   };

//   const InfoItemWithEllipsis = ({ label, value, layout }) => {
//     const textRef = useRef(null);
//     const [isOverflowing, setIsOverflowing] = useState(false);

//     useEffect(() => {
//       const el = textRef.current;
//       if (el) {
//         setIsOverflowing(el.scrollWidth > el.clientWidth);
//       }
//     }, [value]);

//     const textContent = (
//       <Typography
//         ref={textRef}
//         fontSize="12px"
//         color="gray"
//         sx={{
//           maxWidth: 150,
//           overflow: "hidden",
//           textOverflow: "ellipsis",
//           whiteSpace: "nowrap",
//           fontSize: layout === "compact" ? "13px" : "12px",
//         }}
//       >
//         {value}
//       </Typography>
//     );

//     return (
//       <Box display="flex" gap={1} mb={1} alignItems="center">
//         {layout !== "unlabeled" && (
//           <Typography
//             fontSize="12px"
//             color="#607179"
//             fontWeight={600}
//             minWidth={130}
//             sx={{
//               display: layout === "compact" ? "none" : "inline-block",
//               color: layout === "unlabeled" ? "transparent" : "inherit",
//             }}
//           >
//             {label}:
//           </Typography>
//         )}
//         {isOverflowing ? (
//           <Tooltip title={value} arrow>
//             {textContent}
//           </Tooltip>
//         ) : (
//           textContent
//         )}
//       </Box>
//     );
//   };

//   return (
//     <Box
//       role="presentation"
//       display="flex"
//       flexDirection="column"
//       height="100%"
//     >
//       <Box px={3} py={2} flexShrink={0}>
//         <Stack
//           direction="row"
//           justifyContent="space-between"
//           alignItems="center"
//         >
//           <Typography variant="h5">Customise Card</Typography>
//           <Box>
//             <IconButton size="small" onClick={onClose}>
//               <CloseIcon />
//             </IconButton>
//           </Box>
//         </Stack>
//       </Box>

//       <Divider />

//       <Box flexGrow={1} overflow="auto">
//         {/* Card Preview */}
//         <Box sx={{ backgroundColor: "#f2f4f7", px: 2, py: 2 }}>
//           <Typography variant="body1" fontWeight="600" color="gray" mb={2}>
//             Card Preview
//           </Typography>
//           <Card variant="outlined" sx={{ p: 2, borderRadius: 1 }}>
//             <Box
//               display="flex"
//               justifyContent="space-between"
//               alignItems="center"
//             >
//               <Typography variant="body1" fontWeight={600}>
//                 {lockedField}
//               </Typography>
//               <Avatar
//                 sx={{
//                   bgcolor: "#e3eaf6",
//                   color: "#3f51b5",
//                   fontSize: 12,
//                   width: 24,
//                   height: 24,
//                 }}
//               >
//                 JD
//               </Avatar>
//             </Box>

//             {/* Selected Info */}
//             <Box
//               mt={2}
//               sx={{
//                 display: layout === "compact" ? "flex" : "block",
//                 alignItems: "center",
//                 columnGap: 2,
//                 flexWrap: "wrap",
//               }}
//             >
//               {infoItems.map(({ label, value,index }) => (
//                 <InfoItemWithEllipsis
//                   key={index}
//                   label={label}
//                   value={value}
//                   layout={layout}
//                 />
//               ))}
//             </Box>

//             {/* Icons */}
//             <Box
//               mt={3}
//               display="flex"
//               alignItems="center"
//               gap={1.5}
//               flexWrap="wrap"
//             >
//               {icons
//                 .filter((item) => item.enabled)
//                 .map((item) => (
//                   <Tooltip key={item.key} title={item.label} arrow>
//                     <Badge badgeContent={item.count} color="primary">
//                       {item.icon}
//                     </Badge>
//                   </Tooltip>
//                 ))}
//             </Box>
//           </Card>
//         </Box>

//         {/* Accordion Field Groups */}
//         <Box mb={4} sx={{ p: 3 }}>
//           <Typography variant="body1" fontWeight="600" color="gray" mb={2}>
//             Card Layout
//           </Typography>
//           <RadioGroup
//             row
//             value={layout}
//             onChange={(e) => setLayout(e.target.value)}
//             sx={{ mb: 2, justifyContent: "space-between" }}
//           >
//             <FormControlLabel
//               value="default"
//               control={<Radio />}
//               label="Default"
//             />
//             <FormControlLabel
//               value="compact"
//               control={<Radio />}
//               label="Compact"
//             />
//             <FormControlLabel
//               value="unlabeled"
//               control={<Radio />}
//               label="Unlabeled"
//             />
//           </RadioGroup>

//           <Tabs
//             value={tabValue}
//             onChange={handleTabChange}
//             sx={{
//               borderBottom: 1,
//               borderColor: "divider",
//               mb: 2,
//               "& .MuiTabs-indicator": {
//                 backgroundColor: "#2979ff",
//                 height: 2,
//               },
//             }}
//           >
//             <Tab
//               label={
//                 <Typography
//                   fontWeight={600}
//                   sx={{ color: tabValue === 0 ? "#2979ff" : "black" }}
//                 >
//                   Fields ({selectedFields.length} out of {MAX_FIELDS})
//                 </Typography>
//               }
//               sx={{ textTransform: "none", minWidth: 120 }}
//             />
//             <Tab
//               label={
//                 <Typography
//                   fontWeight={600}
//                   sx={{ color: tabValue === 1 ? "#2979ff" : "black" }}
//                 >
//                   Quick actions
//                 </Typography>
//               }
//               sx={{ textTransform: "none", minWidth: 120 }}
//             />
//           </Tabs>

//           {tabValue === 0 && (
//             <Box sx={{ width: "100%" }}>
//               <Box
//                 sx={{
//                   display: "flex",
//                   flexWrap: "wrap",
//                   gap: 1,
//                   mb: 2,
//                 }}
//               >
//                 {selectedFields.map((field) => (
//                   <Chip
//                     key={field}
//                     label={field}
//                     onDelete={() => handleToggle(field)}
//                     variant="outlined"
//                     color="primary"
//                   />
//                 ))}
//               </Box>

//               {allFieldValues.map((groupObj) =>
//                 Object.entries(groupObj).map(([groupName, fields]) => {
//                   const availableFields = fields.filter(
//                     (f) =>
//                       f.label !== lockedField &&
//                       !selectedFields.includes(f.label)
//                   );
//                   return (
//                     <Accordion key={groupName}>
//                       <AccordionSummary expandIcon={<ExpandMoreOutlinedIcon />}>
//                         <Typography fontWeight={600}>{groupName}</Typography>
//                       </AccordionSummary>
//                       <AccordionDetails>
//                         {availableFields.map(({ label }) => (
//                           <Box key={label} display="flex" alignItems="center">
//                             <Checkbox
//                               checked={isFieldSelected(label)}
//                               onChange={() => handleToggle(label)}
//                               disabled={isFieldDisabled(label)}
//                               sx={{ ml: 2 }}
//                             />
//                             <Typography>{label}</Typography>
//                           </Box>
//                         ))}
//                       </AccordionDetails>
//                     </Accordion>
//                   );
//                 })
//               )}
//             </Box>
//           )}
//           {tabValue === 1 && (
//             <Box sx={{ width: "100%" }}>
//               <DragDropContext onDragEnd={handleDragEnd}>
//                 <Droppable droppableId="icons">
//                   {(provided) => (
//                     <Box ref={provided.innerRef} {...provided.droppableProps}>
//                       {icons.map((item, index) => (
//                         <Draggable
//                           draggableId={item.key}
//                           index={index}
//                           key={item.key}
//                         >
//                           {(provided) => (
//                             <Box
//                               ref={provided.innerRef}
//                               {...provided.draggableProps}
//                               display="flex"
//                               alignItems="center"
//                               justifyContent="space-between"
//                               mb={1}
//                               {...provided.dragHandleProps}
//                             >
//                               <Box display="flex" alignItems="center" gap={1}>
//                                 <DragIndicatorOutlinedIcon
//                                   sx={{ color: "#aaa" }}
//                                 />
//                                 <FormControlLabel
//                                   control={
//                                     <Checkbox
//                                       checked={item.enabled}
//                                       onChange={() =>
//                                         handleIconToggle(item.key)
//                                       }
//                                     />
//                                   }
//                                   label={item.label}
//                                 />
//                               </Box>
//                               {item.icon}
//                             </Box>
//                           )}
//                         </Draggable>
//                       ))}
//                       {provided.placeholder}
//                     </Box>
//                   )}
//                 </Droppable>
//               </DragDropContext>
//             </Box>
//           )}
//         </Box>
//       </Box>
//       <Box px={3} py={2} flexShrink={0}>
//         <Stack direction="row" gap={1} justifyContent="end">
//           <Button variant="outlined" onClick={onClose}>
//             Cancel
//           </Button>
//           <Button variant="contained">Apply</Button>
//         </Stack>
//       </Box>
//     </Box>
//   );
// }




import React, { useRef, useEffect, useState } from "react";
import { format } from "date-fns";
import Box from "@mui/material/Box";
import {
  Typography,
  Card,
  Chip,
  Checkbox,
  Tooltip,
  Avatar,
  Badge,
  RadioGroup,
  FormControlLabel,
  Radio,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
  IconButton,
  Stack,
  Button,
  Divider, 
} from "@mui/material";
import { locationservice } from "../services/locationservice";
import CloseIcon from "@mui/icons-material/Close";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import FilterAltOutlinedIcon from "@mui/icons-material/FilterAltOutlined";
import {
  mockFields,
  menuOptions,
  sortOptions,
  allFieldValues,
} from "../contant/mockData";
import SwapVertOutlinedIcon from "@mui/icons-material/SwapVertOutlined";
import BackspaceOutlinedIcon from "@mui/icons-material/BackspaceOutlined";
import SearchOutlinedIcon from "@mui/icons-material/SearchOutlined";
import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
import CallOutlinedIcon from "@mui/icons-material/CallOutlined";
import SmsOutlinedIcon from "@mui/icons-material/SmsOutlined";
import LocalOfferOutlinedIcon from "@mui/icons-material/LocalOfferOutlined";
import DescriptionOutlinedIcon from "@mui/icons-material/DescriptionOutlined";
import CheckBoxOutlinedIcon from "@mui/icons-material/CheckBoxOutlined";
import CalendarMonthOutlinedIcon from "@mui/icons-material/CalendarMonthOutlined";
import ExpandMoreOutlinedIcon from "@mui/icons-material/ExpandMoreOutlined";
import DragIndicatorOutlinedIcon from "@mui/icons-material/DragIndicatorOutlined";

import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

const DragHandle = DragIndicatorOutlinedIcon;

export default function ManageFieldsDrawer({ onClose }) {
  const [manageFields, setManageFieldsOpen] = React.useState(false);
  const toggleManageFieldsDrawer = (newOpen) => () =>
    setManageFieldsOpen(newOpen);

  const [layout, setLayout] = React.useState("default");

  const handleLayoutChange = (_, newLayout) => {
    if (newLayout !== null) setLayout(newLayout);
  };

  const MAX_FIELDS = 7;

  const [selectedFields, setSelectedFields] = React.useState([
    "Opportunity Owner",
    "Business Name",
    "Opportunity Source",
    "Opportunity Value",
    "Lost Reason",
  ]);

  const currentDateTime = format(new Date(), "MMM do, h:mm a");
  const lockedField = "Opportunity Name";

  const handleToggle = (field) => {
    // if (field === lockedField) return;

    setSelectedFields((prev) => {
      if (prev.includes(field)) {
        return prev.filter((f) => f !== field);
      } else if (prev.length < MAX_FIELDS) {
        return [...prev, field];
      }
      return prev;
    });
  };

  const renderInfoItems = () => {
    const result = [];

    allFieldValues.forEach((groupObj) => {
      Object.entries(groupObj).forEach(([groupName, fields]) => {
        fields.forEach(({ label, value }) => {
          if (selectedFields.includes(label)) {
            result.push({ label, value });
          }
        });
      });
    });

    return result;
  };

  const infoItems = renderInfoItems();

  const isFieldSelected = (label) => selectedFields.includes(label);

  const isFieldDisabled = (label) =>
    !isFieldSelected(label) && selectedFields.length >= MAX_FIELDS;

  const [tabValue, setTabValue] = React.useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const iconData = [
    {
      key: "call",
      label: "Call",
      icon: <CallOutlinedIcon sx={{ width: 16, height: 16 }} />,
    },
    {
      key: "chat",
      label: "Unread Conversations",
      icon: <SmsOutlinedIcon sx={{ width: 16, height: 16 }} />,
    },
    {
      key: "tag",
      label: "Tags",
      icon: <LocalOfferOutlinedIcon sx={{ width: 16, height: 16 }} />,
    },
    {
      key: "notes",
      label: "Notes",
      icon: <DescriptionOutlinedIcon sx={{ width: 16, height: 16 }} />,
    },
    {
      key: "tasks",
      label: "Tasks",
      icon: <CheckBoxOutlinedIcon sx={{ width: 16, height: 16 }} />,
    },
    {
      key: "appointments",
      label: "Upcoming Confirmed Appointments",
      icon: <CalendarMonthOutlinedIcon sx={{ width: 16, height: 16 }} />,
    },
  ];
  // Initial list
  const [icons, setIcons] = React.useState(
    iconData.map((item) => ({ ...item, enabled: true, count: 1 }))
  );

  const handleIconToggle = (key) => {
    setIcons((prev) =>
      prev.map((item) =>
        item.key === key ? { ...item, enabled: !item.enabled } : item
      )
    );
  };

  const handleDragEnd = (result) => {
    if (!result.destination) return;
    const reordered = Array.from(icons);
    const [moved] = reordered.splice(result.source.index, 1);
    reordered.splice(result.destination.index, 0, moved);
    setIcons(reordered);
  };

  const InfoItemWithEllipsis = ({ label, value, layout }) => {
    const textRef = useRef(null);
    const [isOverflowing, setIsOverflowing] = useState(false);

    useEffect(() => {
      const el = textRef.current;
      if (el) {
        setIsOverflowing(el.scrollWidth > el.clientWidth);
      }
    }, [value]);

    const textContent = (
      <Typography
        ref={textRef}
        fontSize="12px"
        color="gray"
        sx={{
          maxWidth: 150,
          overflow: "hidden",
          textOverflow: "ellipsis",
          whiteSpace: "nowrap",
          fontSize: layout === "compact" ? "13px" : "12px",
        }}
      >
        {value}
      </Typography>
    );

    return (
      <Box display="flex" gap={1} mb={1} alignItems="center">
        {layout !== "unlabeled" && (
          <Typography
            fontSize="12px"
            color="#607179"
            fontWeight={600}
            minWidth={130}
            sx={{
              display: layout === "compact" ? "none" : "inline-block",
              color: layout === "unlabeled" ? "transparent" : "inherit",
            }}
          >
            {label}:
          </Typography>
        )}
        {isOverflowing ? (
          <Tooltip title={value} arrow>
            {textContent}
          </Tooltip>
        ) : (
          textContent
        )}
      </Box>
    );
  };

  return (
    <Box
      role="presentation"
      display="flex"
      flexDirection="column"
      height="100%"
    >
      <Box px={3} py={2} flexShrink={0}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Typography variant="h5">Customise Card</Typography>
          <Box>
            <IconButton size="small" onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Stack>
      </Box>

      <Divider />

      <Box flexGrow={1} overflow="auto">
        {/* Card Preview */}
        <Box sx={{
          backgroundColor: (theme) => theme.palette.mode === 'dark'
            ? theme.palette.grey[800]
            : "#f2f4f7",
          px: 2,
          py: 2
        }}>
          <Typography variant="body1" fontWeight="600" sx={{
            color: (theme) => theme.palette.text.secondary
          }} mb={2}>
            Card Preview
          </Typography>
          <Card variant="outlined" sx={{ p: 2, borderRadius: 1 }}>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body1" fontWeight={600}>
                {lockedField}
              </Typography>
              <Avatar
                sx={{
                  bgcolor: (theme) => theme.palette.mode === 'dark'
                    ? theme.palette.primary.dark
                    : "#e3eaf6",
                  color: (theme) => theme.palette.mode === 'dark'
                    ? theme.palette.primary.contrastText
                    : "#3f51b5",
                  fontSize: 12,
                  width: 24,
                  height: 24,
                }}
              >
                JD
              </Avatar>
            </Box>

            {/* Selected Info */}
            <Box
              mt={2}
              sx={{
                display: layout === "compact" ? "flex" : "block",
                alignItems: "center",
                columnGap: 2,
                flexWrap: "wrap",
              }}
            >
              {infoItems.map(({ label, value,index }) => (
                <InfoItemWithEllipsis
                  key={index}
                  label={label}
                  value={value}
                  layout={layout}
                />
              ))}
            </Box>

            {/* Icons */}
            <Box
              mt={3}
              display="flex"
              alignItems="center"
              gap={1.5}
              flexWrap="wrap"
            >
              {icons
                .filter((item) => item.enabled)
                .map((item) => (
                  <Tooltip key={item.key} title={item.label} arrow>
                    <Badge badgeContent={item.count} color="primary">
                      {item.icon}
                    </Badge>
                  </Tooltip>
                ))}
            </Box>
          </Card>
        </Box>

        {/* Accordion Field Groups */}
        <Box mb={4} sx={{ p: 3 }}>
          <Typography variant="body1" fontWeight="600" sx={{
            color: (theme) => theme.palette.text.secondary
          }} mb={2}>
            Card Layout
          </Typography>
          <RadioGroup
            row
            value={layout}
            onChange={(e) => setLayout(e.target.value)}
            sx={{ mb: 2, justifyContent: "space-between" }}
          >
            <FormControlLabel
              value="default"
              control={<Radio />}
              label="Default"
            />
            <FormControlLabel
              value="compact"
              control={<Radio />}
              label="Compact"
            />
            <FormControlLabel
              value="unlabeled"
              control={<Radio />}
              label="Unlabeled"
            />
          </RadioGroup>

          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{
              borderBottom: 1,
              borderColor: "divider",
              mb: 2,
              "& .MuiTabs-indicator": {
                backgroundColor: "#2979ff",
                height: 2,
              },
            }}
          >
            <Tab
              label={
                <Typography
                  fontWeight={600}
                  sx={{
                    color: tabValue === 0
                      ? "#2979ff"
                      : (theme) => theme.palette.text.primary
                  }}
                >
                  Fields ({selectedFields.length} out of {MAX_FIELDS})
                </Typography>
              }
              sx={{ textTransform: "none", minWidth: 120 }}
            />
            <Tab
              label={
                <Typography
                  fontWeight={600}
                  sx={{
                    color: tabValue === 1
                      ? "#2979ff"
                      : (theme) => theme.palette.text.primary
                  }}
                >
                  Quick actions
                </Typography>
              }
              sx={{ textTransform: "none", minWidth: 120 }}
            />
          </Tabs>

          {tabValue === 0 && (
            <Box sx={{ width: "100%" }}>
              <Box
                sx={{
                  display: "flex",
                  flexWrap: "wrap",
                  gap: 1,
                  mb: 2,
                }}
              >
                {selectedFields.map((field) => (
                  <Chip
                    key={field}
                    label={field}
                    onDelete={() => handleToggle(field)}
                    variant="outlined"
                    color="primary"
                  />
                ))}
              </Box>

              {allFieldValues.map((groupObj) =>
                Object.entries(groupObj).map(([groupName, fields]) => {
                  const availableFields = fields.filter(
                    (f) =>
                      f.label !== lockedField &&
                      !selectedFields.includes(f.label)
                  );
                  return (
                    <Accordion key={groupName}>
                      <AccordionSummary expandIcon={<ExpandMoreOutlinedIcon />}>
                        <Typography fontWeight={600}>{groupName}</Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        {availableFields.map(({ label }) => (
                          <Box key={label} display="flex" alignItems="center">
                            <Checkbox
                              checked={isFieldSelected(label)}
                              onChange={() => handleToggle(label)}
                              disabled={isFieldDisabled(label)}
                              sx={{ ml: 2 }}
                            />
                            <Typography>{label}</Typography>
                          </Box>
                        ))}
                      </AccordionDetails>
                    </Accordion>
                  );
                })
              )}
            </Box>
          )}
          {tabValue === 1 && (
            <Box sx={{ width: "100%" }}>
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="icons">
                  {(provided) => (
                    <Box ref={provided.innerRef} {...provided.droppableProps}>
                      {icons.map((item, index) => (
                        <Draggable
                          draggableId={item.key}
                          index={index}
                          key={item.key}
                        >
                          {(provided) => (
                            <Box
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              display="flex"
                              alignItems="center"
                              justifyContent="space-between"
                              mb={1}
                              {...provided.dragHandleProps}
                            >
                              <Box display="flex" alignItems="center" gap={1}>
                                <DragIndicatorOutlinedIcon
                                  sx={{ color: "#aaa" }}
                                />
                                <FormControlLabel
                                  control={
                                    <Checkbox
                                      checked={item.enabled}
                                      onChange={() =>
                                        handleIconToggle(item.key)
                                      }
                                    />
                                  }
                                  label={item.label}
                                />
                              </Box>
                              {item.icon}
                            </Box>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </Box>
                  )}
                </Droppable>
              </DragDropContext>
            </Box>
          )}
        </Box>
      </Box>
      <Box px={3} py={2} flexShrink={0}>
        <Stack direction="row" gap={1} justifyContent="end">
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="contained">Apply</Button>
        </Stack>
      </Box>
    </Box>
  );
}
