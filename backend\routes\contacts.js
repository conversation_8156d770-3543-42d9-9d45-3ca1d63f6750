const express = require("express");
const router = express.Router();
require("dotenv").config();

const app = express();
app.use(express.json());

const GHL_API_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6InQ1cjFZQlpreERDV1VPMkZ5RGtOIiwiY29tcGFueV9pZCI6IjA5bnRJc1JieDRnQXhjaGJIcWU3IiwidmVyc2lvbiI6MSwiaWF0IjoxNjg1NzI3MjA5NjYxLCJzdWIiOiJ1c2VyX2lkIn0.-UcbUyP-4nxElkvgSfm8s_78bk0cWb3A44B_vZj-29s";

// Get contacts with pagination
router.post("/contacts", async (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ error: "Missing Authorization header" });
  }

  const { locationId, page = 1, limit = 100,query } = req.body;

  if (!locationId) {
    return res.status(400).json({ error: "Missing locationId in request body" });
  }

  try {
    const url = 'https://services.leadconnectorhq.com/contacts/search';
    // const url = 'https://backend.leadconnectorhq.com/contacts/search/2';
    const options = {
      method: 'POST',
      headers: {
        Authorization: authHeader,
        Version: '2021-07-28',
        'Content-Type': 'application/json',
        Accept: 'application/json'
      },
      body: JSON.stringify({
        locationId,
        pageLimit: limit,
        page: page,
        query:query
      })
    };

    console.log('Making GHL API request with options:', {
      url,
      headers: options.headers,
      body: JSON.parse(options.body)
    });

    const response = await fetch(url, options);
    const data = await response.json();

    console.log('GHL API Response:', data);

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const contacts = data.contacts || [];
    const totalContacts = data.total || 0;
    const hasMore = (page * limit) < totalContacts;

    // Return contacts with metadata for pagination
    res.json({
      contacts: contacts,
      meta: {
        total: totalContacts,
        hasMore: hasMore,
        currentPage: page,
        limit: parseInt(limit),
        loadedCount: contacts.length
      }
    });
  } catch (error) {
    console.error("Error fetching contacts:", error);
    res.status(500).json({ error: "Failed to fetch contacts data" });
  }
});

module.exports = router;
