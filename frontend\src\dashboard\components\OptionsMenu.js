import * as React from 'react';
import { styled } from '@mui/material/styles';
import Divider, { dividerClasses } from '@mui/material/Divider';
import Menu from '@mui/material/Menu';
import MuiMenuItem from '@mui/material/MenuItem';
import { paperClasses } from '@mui/material/Paper';
import { listClasses } from '@mui/material/List';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon, { listItemIconClasses } from '@mui/material/ListItemIcon';
import LogoutRoundedIcon from '@mui/icons-material/LogoutRounded';
import MoreVertRoundedIcon from '@mui/icons-material/MoreVertRounded';
import MenuButton from './MenuButton';
import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';

const MenuItem = styled(MuiMenuItem)({
  margin: '2px 0',
});

export default function OptionsMenu() {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };


  
  // 🔁 Idle timeout constant
  const IDLE_TIMEOUT = 15 * 60 * 1000; // 15 mins
  // const IDLE_TIMEOUT = 5 * 1000; // ⏱️ 10 seconds for testing
  
  // 🔁 Declare timer
  let idleTimer = null;
  
  // 🔁 Custom hook directly inside the filelastPath
  const useIdleLogout = (logoutFn) => {
    useEffect(() => {
      const resetTimer = () => {
        if (idleTimer) clearTimeout(idleTimer);
  
        idleTimer = setTimeout(() => {
          logoutFn(); // Auto logout on inactivity
        }, IDLE_TIMEOUT);
      };
  
      const events = ["mousemove", "keydown", "scroll", "click", "touchstart"];
      events.forEach((event) => window.addEventListener(event, resetTimer));
      resetTimer();
  
      return () => {
        if (idleTimer) clearTimeout(idleTimer);
        events.forEach((event) => window.removeEventListener(event, resetTimer));
      };
    }, [logoutFn]);
  };
  
  // 🔁 Logout function
  const handleLogout = () => {
    const muiMode = localStorage.getItem("mui-mode");
  
    // Don't use localStorage for logout flag, use sessionStorage
    sessionStorage.setItem("logoutFlag", "true");
  
    localStorage.clear();
  
    if (muiMode !== null) {
      localStorage.setItem("mui-mode", muiMode);
    }
  
    window.location.reload();
  };
  return (
    <React.Fragment>
      <MenuButton
        aria-label="Open menu"
        onClick={handleClick}
        sx={{ borderColor: 'transparent' }}
      >
        <MoreVertRoundedIcon />
      </MenuButton>
      <Menu
        anchorEl={anchorEl}
        id="menu"
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        sx={{
          [`& .${listClasses.root}`]: {
            padding: '4px',
          },
          [`& .${paperClasses.root}`]: {
            padding: 0,
          },
          [`& .${dividerClasses.root}`]: {
            margin: '4px -4px',
          },
        }}
      >
        <MenuItem onClick={handleClose}>Profile</MenuItem>
        <MenuItem onClick={handleClose}>My account</MenuItem>
        <Divider />
        <MenuItem onClick={handleClose}>Add another account</MenuItem>
        <MenuItem onClick={handleClose}>Settings</MenuItem>
        <Divider />
        <MenuItem
          onClick={() => handleLogout()}
          sx={{
            [`& .${listItemIconClasses.root}`]: {
              ml: 'auto',
              minWidth: 0,
            },
          }}
        >
          <ListItemText>Logout</ListItemText>
          <ListItemIcon>
            <LogoutRoundedIcon fontSize="small" />
          </ListItemIcon>
        </MenuItem>
      </Menu>
    </React.Fragment>
  );
}
