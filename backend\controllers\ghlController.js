const { readTokens, saveTokens } = require('../utils/tokenUtils');
require('dotenv').config();

const CLIENT_ID = process.env.GHL_CLIENT_ID;
const CLIENT_SECRET = process.env.GHL_CLIENT_SECRET;
const REDIRECT_URI = process.env.GHL_REDIRECT_URI;
const FRONTEND_URL = process.env.FRONTEND_URL;

// Step 1: Redirect User for Authentication
const ghlAuth = (req, res) => {
  const scopes = 'locations.readonly businesses.readonly';
  const authUrl = `https://marketplace.gohighlevel.com/oauth/chooselocation?response_type=code&client_id=${CLIENT_ID}&redirect_uri=${REDIRECT_URI}&scope=${scopes}`;
  res.json({ authUrl });
};

// Step 2: Handle OAuth Callback & Exchange Code for Token
const ghlCallback = async (req, res) => {
  const { code } = req.query;

  if (!code) {
    return res.status(400).json({ error: 'Authorization code is missing' });
  }

  try {
    const response = await fetch('https://services.leadconnectorhq.com/oauth/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        client_id: process.env.GHL_CLIENT_ID,      // ✅ Use environment variables
        client_secret: process.env.GHL_CLIENT_SECRET,
        grant_type: 'authorization_code',
        code,
        redirect_uri: process.env.GHL_REDIRECT_URI
      })
    });

    const responseData = await response.json();

    console.log('GHL OAuth Response:', responseData); // ✅ Debugging: Log full response

    if (!response.ok) {
      throw new Error(`OAuth API Error: ${responseData.error_description || responseData.error || 'Unknown error'}`);
    }

    const { access_token, refresh_token } = responseData;

    if (!access_token) {
      throw new Error('Failed to retrieve access token');
    }

    // ✅ Securely store the refresh token in an HttpOnly cookie
    res.cookie('refresh_token', refresh_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'Strict'
    });

    // ✅ Redirect to frontend with the access token
    res.redirect(`${FRONTEND_URL}/dashboard?token=${access_token}`);
  } catch (error) {
    console.error('OAuth Error:', error);
    res.redirect(`${FRONTEND_URL}/dashboard?error=auth_failed`);
  }
};

// Step 3: Refresh Token When Expired
const refreshToken = async (req, res) => {
  try {
    const refresh_token = req.cookies.refresh_token;

    if (!refresh_token) {
      return res.status(401).json({ error: 'No refresh token available' });
    }

    const response = await fetch('https://services.leadconnectorhq.com/oauth/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        grant_type: 'refresh_token',
        refresh_token: refresh_token
      })
    });

    const { access_token, refresh_token: new_refresh_token } = await response.json();

    if (!access_token) {
      throw new Error('Failed to refresh token');
    }

    // Update refresh token in cookie
    res.cookie('refresh_token', new_refresh_token, {
      httpOnly: true,
      secure: true,
      sameSite: 'Strict'
    });

    res.json({ access_token });
  } catch (error) {
    console.error('Token Refresh Error:', error);
    res.status(500).json({ error: 'Token refresh failed' });
  }
};

// Step 4: Fetch GHL Locations
const getLocations = async () => {
  let token = localStorage.getItem('access_token');

  if (!token) {
    console.error('No access token found');
    return { error: 'Unauthorized: No token available' };
  }

  let response = await fetch('https://rest.gohighlevel.com/v1/locations/', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  if (response.status === 401) {
    console.log('Access token expired, attempting refresh...');
    const refreshResponse = await fetch('http://localhost:5000/api/auth/refresh-token', {
      credentials: 'include'  // ✅ Important for HttpOnly cookies
    });

    const newTokenData = await refreshResponse.json();

    if (newTokenData.access_token) {
      localStorage.setItem('access_token', newTokenData.access_token);
      token = newTokenData.access_token;

      response = await fetch('https://rest.gohighlevel.com/v1/locations/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
    } else {
      console.error('Token refresh failed');
      return { error: 'Token refresh failed' };
    }
  }

  return await response.json();
};

module.exports = { ghlAuth, ghlCallback, refreshToken, getLocations };

