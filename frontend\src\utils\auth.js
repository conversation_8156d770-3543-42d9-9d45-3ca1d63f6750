// auth.js - Utility functions for authentication

// Initialize the localStorage with sample users
export const initializeUsers = () => {
  // Check if we've already initialized users
  if (!localStorage.getItem("usersInitialized")) {
    const sampleUsers = [
      {
        id: "1",
        name: "Test User",
        email: "<EMAIL>",
        password: "123",
      },
      {
        id: "2",
        name: "Another User",
        email: "<EMAIL>",
        password: "user123",
      },
    ];

    localStorage.setItem("users", JSON.stringify(sampleUsers));
    localStorage.setItem("usersInitialized", "true");
    console.log("Sample users initialized in localStorage");
  }
};

// Check if a user is authenticated
export const isAuthenticated = () => {
  const user =
    localStorage.getItem("currentUser") ||
    sessionStorage.getItem("currentUser");
  return !!user;
};

// Get the current user
export const getCurrentUser = () => {
  const userStr =
    localStorage.getItem("currentUser") ||
    sessionStorage.getItem("currentUser");
  return userStr ? JSON.parse(userStr) : null;
};

// Log out the user
export const logout = () => {
  // localStorage.removeItem("currentUser");
  // sessionStorage.removeItem("currentUser");
  window.location.href = "/sign-in"; // Redirect to login page
};

// Register a new user (using localStorage)
export const registerUser = (userData) => {
  const users = JSON.parse(localStorage.getItem("users") || "[]");

  // Check if email already exists
  if (users.some((user) => user.email === userData.email)) {
    throw new Error("Email already registered");
  }

  const newUser = {
    id: Date.now().toString(),
    name: userData.name,
    email: userData.email,
    password: userData.password,
  };

  users.push(newUser);
  localStorage.setItem("users", JSON.stringify(users));

  return { success: true };
};
