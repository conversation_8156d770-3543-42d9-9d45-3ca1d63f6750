import * as React from 'react';
import { DataGrid } from '@mui/x-data-grid';
// import { columns, rows } from '../internals/data/gridData';
import Avatar from '@mui/material/Avatar';
import Chip from '@mui/material/Chip';
import { useNavigate } from 'react-router';


export default function CustomizedDataGrid({ locationData }) {
  const navigate = useNavigate(); // Initialize navigate

  const columns = [
    // { field: 'id', headerName: 'ID', flex: 1, minWidth: 200 },
    { field: 'name', headerName: 'Full Name', flex: 1.5, minWidth: 200 },
    { field: 'email', headerName: 'Email', flex: 1.5, minWidth: 250 },
    { field: 'phone', headerName: 'Phone', flex: 1, minWidth: 150 },
    { field: 'address', headerName: 'Address', flex: 2, minWidth: 250 },
    { field: 'city', headerName: 'City', flex: 1, minWidth: 150 },
    { field: 'state', headerName: 'State', flex: 1, minWidth: 120 },
    { field: 'country', headerName: 'Country', flex: 1, minWidth: 100 },
  ];

  return (
    <DataGrid
      checkboxSelection
      rows={locationData}
      columns={columns}
      onRowClick={(params) => navigate(`/${params.row.id}/sub-account-detail/`)}
      getRowClassName={(params) =>
        params.indexRelativeToCurrentPage % 2 === 0 ? 'even' : 'odd'
      }
      initialState={{
        pagination: { paginationModel: { pageSize: 20 } },
      }}
      pageSizeOptions={[10, 20, 50]}
      disableColumnResize
      density="compact"
      autoHeight
      sx={{
        minHeight: 400,
        '& .MuiDataGrid-main': {
          minHeight: 400,
        },
        '& .MuiDataGrid-virtualScroller': {
          minHeight: '300px !important',
          pb: '10px'
        },
        '& .MuiDataGrid-footerContainer': {
          minHeight: 52,
        },
      }}
      slotProps={{
        filterPanel: {
          filterFormProps: {
            logicOperatorInputProps: {
              variant: 'outlined',
              size: 'small',
            },
            columnInputProps: {
              variant: 'outlined',
              size: 'small',
              sx: { mt: 'auto' },
            },
            operatorInputProps: {
              variant: 'outlined',
              size: 'small',
              sx: { mt: 'auto' },
            },
            valueInputProps: {
              InputComponentProps: {
                variant: 'outlined',
                size: 'small',
              },
            },
          },
        },
      }}
    />
  );
}


