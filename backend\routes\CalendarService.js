const express = require("express");
const router = express.Router();
require("dotenv").config();

const app = express();
app.use(express.json());

const GHL_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6InQ1cjFZQlpreERDV1VPMkZ5RGtOIiwiY29tcGFueV9pZCI6IjA5bnRJc1JieDRnQXhjaGJIcWU3IiwidmVyc2lvbiI6MSwiaWF0IjoxNjg1NzI3MjA5NjYxLCJzdWIiOiJ1c2VyX2lkIn0.-UcbUyP-4nxElkvgSfm8s_78bk0cWb3A44B_vZj-29s";

// Get all calendar services
router.get("/calendars/services", async (req, res) => {
    try {
        const response = await fetch("https://rest.gohighlevel.com/v1/calendars/services", {
            method: "GET",
            headers: {
                Authorization: `Bearer ${GHL_API_KEY}`,
                "Content-Type": "application/json",
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error("Error fetching calendar services:", error.message);
        res.status(500).json({ error: "Failed to fetch calendar services" });
    }
});

module.exports = router;
