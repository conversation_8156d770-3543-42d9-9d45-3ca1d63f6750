const express = require("express");
const router = express.Router();
require("dotenv").config();

const app = express();
app.use(express.json());

const GHL_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb21wYW55X2lkIjoiMDludElzUmJ4NGdBeGNoYkhxZTciLCJ2ZXJzaW9uIjoxLCJpYXQiOjE3Mzk3ODY0Mjc5MjAsInN1YiI6IlFScnA2WDE2RVRSNXNndWx0SWFxIn0.1j1oqgkqZA_5Db-ioQeWm0IMcoMOhL-j-YcOOmp0K2w";

// Get all locations
router.get("/locations", async (req, res) => {
  try {
    const response = await fetch("https://rest.gohighlevel.com/v1/locations/", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${GHL_API_KEY}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error("Error fetching locations:", error);
    res.status(500).json({ error: "Failed to fetch locations" });
  }
});

// Get tasks for a specific contact
router.get("/contacts/:id/tasks", async (req, res) => {
  const contactId = req.params.id;
  try {
    const response = await fetch(`https://rest.gohighlevel.com/v1/contacts/${contactId}/tasks`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${GHL_API_KEY}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error("Error fetching tasks:", error);
    res.status(500).json({ error: "Failed to fetch tasks" });
  }
});

module.exports = router;
