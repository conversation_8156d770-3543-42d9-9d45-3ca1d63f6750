// import React, { useEffect, useState } from "react";
// import axios from "axios";

// const MonthlyRevenue = () => {
//   const [revenue, setRevenue] = useState(null);
//   const [error, setError] = useState(null);

//   useEffect(() => {
//     axios.get(`http://localhost:5000/api/payments/transactions?altId=t5r1YBZkxDCWUO2FyDkN&altType=location`) // Call backend API
//       .then((response) => setRevenue(response.data))
//       .catch((error) => {
//         console.error("Error fetching revenue:", error.response ? error.response.data : error.message);
//         setError("Failed to load revenue data.");
//       });
//   }, []);

//   return (
//     <div>
//       <h2>Monthly Revenue</h2>
//       {error ? (
//         <p style={{ color: "red" }}>{error}</p>
//       ) : revenue ? (
//         <p>Total Revenue for {revenue.month}/{revenue.year}: <strong>${revenue.totalRevenue}</strong></p>
//       ) : (
//         <p>Loading revenue data...</p>
//       )}
//     </div>
//   );
// };

// export default MonthlyRevenue;


import React, { useState, useEffect } from "react";
import axios from "axios";

const MonthlyRevenue = () => {
  const [revenueData, setRevenueData] = useState(null);
  const [loading, setLoading] = useState(true);

  const NEW_API_URL = process.env.REACT_APP_API_BASE_URL;

  useEffect(() => {
    const fetchRevenue = async () => {
      try {
        const response = await axios.get(`${NEW_API_URL}/api/revenue`);
        setRevenueData(response.data);
      } catch (error) {
        console.error("Error fetching revenue:", error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchRevenue();
  }, []);

  return (
    <div style={{ textAlign: "center", padding: "20px" }}>
      <h1>GHL Monthly Revenue</h1>
      {loading ? (
        <p>Loading...</p>
      ) : (
        <div>
          <p><strong>Subscription Revenue:</strong> ${revenueData?.subscriptions}</p>
          <p><strong>One-Time Payments:</strong> ${revenueData?.payments}</p>
          <p><strong>Affiliate Earnings:</strong> ${revenueData?.affiliateEarnings}</p>
          <h2><strong>Total Monthly Revenue:</strong> ${revenueData?.totalRevenue}</h2>
        </div>
      )}
    </div>
  );
};

export default MonthlyRevenue;
