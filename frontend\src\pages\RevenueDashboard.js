import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar
} from 'recharts';

// Backend API URL - change this to match your server
const API_URL = 'http://localhost:5000/api';
const NEW_API_URL = process.env.REACT_APP_API_BASE_URL;

const RevenueDashboard = () => {
  // State variables
  const [revenueData, setRevenueData] = useState([]);
  const [summary, setSummary] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [months, setMonths] = useState(12);
  const [selectedMonth, setSelectedMonth] = useState(null);
  const [monthDetail, setMonthDetail] = useState(null);

  // Format currency values
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount || 0);
  };

  // Fetch revenue overview data
  const fetchRevenueData = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(`${API_URL}/revenue`, {
        params: { months }
      });

      setRevenueData(response.data.data);
      setSummary(response.data.summary);
    } catch (err) {
      console.error('Error fetching revenue data:', err);
      setError('Failed to load revenue data. Please check server connection.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch specific month detail
  const fetchMonthDetail = async (monthData) => {
    if (!monthData) return;

    setLoading(true);

    try {
      // Extract year and month from the month string
      const date = new Date(monthData.month);
      const yearMonth = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      const response = await axios.get(`${API_URL}/revenue/${yearMonth}`);

      setMonthDetail(response.data);
      setSelectedMonth(monthData.month);
    } catch (err) {
      console.error('Error fetching month detail:', err);
      setError('Failed to load month detail');
    } finally {
      setLoading(false);
    }
  };

  // Initial data load
  useEffect(() => {
    fetchRevenueData();
  }, [months]);

  // Custom tooltip component for charts
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="custom-tooltip" style={{
          backgroundColor: '#fff',
          padding: '10px',
          border: '1px solid #ccc',
          borderRadius: '5px'
        }}>
          <p className="label">{label}</p>
          {payload.map((entry, index) => (
            <p key={`item-${index}`} style={{ color: entry.color }}>
              {entry.name}: {formatCurrency(entry.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Loading indicator
  if (loading && revenueData.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <h2>Loading Revenue Data...</h2>
        <p>Please wait while we fetch your GHL revenue information.</p>
      </div>
    );
  }

  // Error display
  if (error && revenueData.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '50px', color: '#e74c3c' }}>
        <h2>Error Loading Data</h2>
        <p>{error}</p>
        <button
          onClick={fetchRevenueData}
          style={{
            padding: '10px 20px',
            backgroundColor: '#3498db',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginTop: '20px'
          }}
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div style={{
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
      maxWidth: '1200px',
      margin: '0 auto',
      padding: '20px'
    }}>
      <h1 style={{ textAlign: 'center', marginBottom: '30px' }}>GHL Monthly Revenue Dashboard</h1>

      {/* Summary Cards */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        marginBottom: '30px'
      }}>
        <div style={{
          flex: 1,
          background: 'white',
          borderRadius: '8px',
          padding: '20px',
          margin: '0 10px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <h3 style={{ fontSize: '16px', color: '#7f8c8d', marginBottom: '10px' }}>Total Revenue</h3>
          <p style={{ fontSize: '28px', fontWeight: 'bold', color: '#2980b9', margin: '0' }}>
            {formatCurrency(summary.totalRevenue)}
          </p>
        </div>

        <div style={{
          flex: 1,
          background: 'white',
          borderRadius: '8px',
          padding: '20px',
          margin: '0 10px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <h3 style={{ fontSize: '16px', color: '#7f8c8d', marginBottom: '10px' }}>Average Monthly</h3>
          <p style={{ fontSize: '28px', fontWeight: 'bold', color: '#2980b9', margin: '0' }}>
            {formatCurrency(summary.avgMonthlyRevenue)}
          </p>
        </div>

        <div style={{
          flex: 1,
          background: 'white',
          borderRadius: '8px',
          padding: '20px',
          margin: '0 10px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          textAlign: 'center'
        }}>
          <h3 style={{ fontSize: '16px', color: '#7f8c8d', marginBottom: '10px' }}>Months Analyzed</h3>
          <p style={{ fontSize: '28px', fontWeight: 'bold', color: '#2980b9', margin: '0' }}>
            {summary.monthsAnalyzed || 0}
          </p>
        </div>
      </div>

      {/* Controls */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        margin: '30px 0',
        padding: '15px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px'
      }}>
        <div>
          <label sx={{
            '&.Mui-focused': {
              // backgroundColor: '#fff',
              backgroundColor: (theme) => theme.palette.background.paper,
              px: 0.5
            }
          }}>
            Show last
            <select
              value={months}
              onChange={(e) => setMonths(parseInt(e.target.value))}
              style={{
                margin: '0 10px',
                padding: '8px 12px',
                borderRadius: '4px',
                border: '1px solid #ddd'
              }}
            >
              <option value="3">3 months</option>
              <option value="6">6 months</option>
              <option value="12">12 months</option>
              <option value="24">24 months</option>
            </select>
          </label>
        </div>

        <button
          onClick={fetchRevenueData}
          style={{
            padding: '8px 16px',
            backgroundColor: '#3498db',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Refresh Data
        </button>
      </div>

      {/* Revenue Trend Chart */}
      <div style={{
        background: 'white',
        borderRadius: '8px',
        padding: '20px',
        marginBottom: '30px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      }}>
        <h2 style={{ marginBottom: '20px' }}>Revenue Trend</h2>
        <ResponsiveContainer width="100%" height={400}>
          <LineChart
            data={revenueData}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis
              tickFormatter={(value) => `$${value.toLocaleString()}`}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line
              type="monotone"
              dataKey="opportunityRevenue"
              name="Opportunity Revenue"
              stroke="#8884d8"
              activeDot={{ r: 8, onClick: (_, index) => fetchMonthDetail(revenueData[index]) }}
            />
            <Line
              type="monotone"
              dataKey="paymentsRevenue"
              name="Payments Revenue"
              stroke="#82ca9d"
            />
            <Line
              type="monotone"
              dataKey="totalRevenue"
              name="Total Revenue"
              stroke="#ff7300"
              strokeWidth={2}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Revenue Distribution Chart */}
      <div style={{
        background: 'white',
        borderRadius: '8px',
        padding: '20px',
        marginBottom: '30px',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
      }}>
        <h2 style={{ marginBottom: '20px' }}>Revenue Distribution</h2>
        <ResponsiveContainer width="100%" height={400}>
          <BarChart
            data={revenueData}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis
              tickFormatter={(value) => `$${value.toLocaleString()}`}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar
              dataKey="opportunityRevenue"
              name="Opportunity Revenue"
              fill="#8884d8"
              onClick={(data) => fetchMonthDetail(data)}
            />
            <Bar
              dataKey="paymentsRevenue"
              name="Payments Revenue"
              fill="#82ca9d"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Month Detail Section */}
      {monthDetail && (
        <div style={{
          background: 'white',
          borderRadius: '8px',
          padding: '20px',
          marginTop: '40px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
        }}>
          <h2 style={{ marginBottom: '20px' }}>Detail for {monthDetail.month}</h2>

          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            margin: '20px 0'
          }}>
            <div style={{
              flex: 1,
              textAlign: 'center',
              padding: '15px',
              borderRadius: '8px',
              margin: '0 10px',
              backgroundColor: '#f8f9fa'
            }}>
              <h3 style={{ fontSize: '16px', color: '#7f8c8d', marginBottom: '10px' }}>
                Opportunity Revenue
              </h3>
              <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#2980b9', margin: '5px 0' }}>
                {formatCurrency(monthDetail.opportunityRevenue)}
              </p>
              <p style={{ fontSize: '14px', color: '#95a5a6', margin: '5px 0' }}>
                {monthDetail.opportunitiesCount} opportunities
              </p>
            </div>

            <div style={{
              flex: 1,
              textAlign: 'center',
              padding: '15px',
              borderRadius: '8px',
              margin: '0 10px',
              backgroundColor: '#f8f9fa'
            }}>
              <h3 style={{ fontSize: '16px', color: '#7f8c8d', marginBottom: '10px' }}>
                Payments Revenue
              </h3>
              <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#2980b9', margin: '5px 0' }}>
                {formatCurrency(monthDetail.paymentsRevenue)}
              </p>
              <p style={{ fontSize: '14px', color: '#95a5a6', margin: '5px 0' }}>
                {monthDetail.paymentsCount} payments
              </p>
            </div>

            <div style={{
              flex: 1,
              textAlign: 'center',
              padding: '15px',
              borderRadius: '8px',
              margin: '0 10px',
              backgroundColor: '#f8f9fa'
            }}>
              <h3 style={{ fontSize: '16px', color: '#7f8c8d', marginBottom: '10px' }}>
                Total Revenue
              </h3>
              <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#2980b9', margin: '5px 0' }}>
                {formatCurrency(monthDetail.totalRevenue)}
              </p>
            </div>
          </div>

          <div style={{ textAlign: 'center', marginTop: '20px' }}>
            <button
              onClick={() => {
                setSelectedMonth(null);
                setMonthDetail(null);
              }}
              style={{
                padding: '8px 16px',
                backgroundColor: '#e74c3c',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Close Detail
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default RevenueDashboard;