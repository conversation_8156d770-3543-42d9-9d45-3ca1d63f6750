// src/utils/RedirectToLastPath.js
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";

const RedirectToLastPath = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (location.pathname === "/") {
      const lastPath = localStorage.getItem("lastPath");
      const selectedLocationId = localStorage.getItem("selectedLocationId");

      if (lastPath && lastPath !== "/") {
        const segments = lastPath.split("/").filter(Boolean); // ["abc123", "contacts"]
        if (segments.length >= 1) {
          const locationId = segments[0];
          navigate(`/${locationId}`, { replace: true });
          return;
        }
      }

      // If lastPath doesn't exist or is invalid
      if (selectedLocationId) {
        navigate(`/${selectedLocationId}`, { replace: true });
      } else {
        navigate("/select-sub-account", { replace: true });
      }
    }
  }, [location.pathname, navigate]);

  return null;
};

export default RedirectToLastPath;
