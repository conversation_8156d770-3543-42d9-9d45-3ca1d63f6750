const express = require('express');
const router = express.Router();
require('dotenv').config();

// Constants for GHL API
const GHL_BASE_URL = 'https://services.leadconnectorhq.com/';
const GHL_API_KEY = process.env.GHL_API_KEY;
const COMPANY_ID = '09ntIsRbx4gAxchbHqe7'; // Add this to your .env file
const ACCOUNT_ID = '1FFSHNBDlnFv4Qf8'; // Add this to your .env file

// MRR endpoint to get data directly from GHL
router.get('/mrr/direct', async (req, res) => {
  try {
    const today = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(today.getMonth() - 6);

    const from = req.query.from || sixMonthsAgo.toISOString().split('T')[0];
    const to = req.query.to || today.toISOString().split('T')[0];
    const type = req.query.type || 'Monthly';
    const timezone = req.query.timezone || 'America/New_York';

    console.log(`Fetching MRR data from ${from} to ${to} with type ${type}`);

    const url = `${GHL_BASE_URL}agency-dashboard-api/account-revenue/acct_${ACCOUNT_ID}/monthly-mrr?companyId=${COMPANY_ID}&from=${from}&to=${to}&type=${type}&timezone=${timezone}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': GHL_API_KEY, // No "Bearer" prefix based on original note
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch data: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error fetching MRR data:', error.message);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch MRR data',
      error: error.message,
    });
  }
});

// Alternative authentication approach
router.get('/mrr/direct-with-location-token', async (req, res) => {
  try {
    const today = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(today.getMonth() - 6);

    const from = req.query.from || sixMonthsAgo.toISOString().split('T')[0];
    const to = req.query.to || today.toISOString().split('T')[0];
    const type = req.query.type || 'Monthly';
    const timezone = req.query.timezone || 'America/New_York';

    const url = `${GHL_BASE_URL}agency-dashboard-api/account-revenue/acct_${ACCOUNT_ID}/monthly-mrr?companyId=${COMPANY_ID}&from=${from}&to=${to}&type=${type}&timezone=${timezone}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${GHL_API_KEY}`, // Using Bearer prefix
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch data: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error with location token method:', error.message);
    res.status(500).json({
      status: 'error',
      message: 'Failed with location token method',
      error: error.message,
    });
  }
});

// Test authentication with different token formats
router.get('/test-auth', async (req, res) => {
  try {
    let response, methodUsed;

    // Try without Bearer prefix
    try {
      response = await fetch(`${GHL_BASE_URL}locations/`, {
        method: 'GET',
        headers: {
          'Authorization': GHL_API_KEY,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        methodUsed = 'Direct token without Bearer prefix';
      } else {
        throw new Error('Direct token method failed');
      }
    } catch (err1) {
      console.log('First method failed:', err1.message);

      // Try with Bearer prefix
      response = await fetch(`${GHL_BASE_URL}locations/`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${GHL_API_KEY}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        methodUsed = 'Token with Bearer prefix';
      } else {
        throw new Error('Bearer token method failed');
      }
    }

    const data = await response.json();
    res.json({
      success: true,
      method: methodUsed,
      locations: data.locations ? data.locations.length : 0,
    });
  } catch (error) {
    console.error('All auth methods failed:', error.message);
    res.status(500).json({
      success: false,
      message: 'All authentication methods failed',
      error: error.message,
    });
  }
});

module.exports = router;
