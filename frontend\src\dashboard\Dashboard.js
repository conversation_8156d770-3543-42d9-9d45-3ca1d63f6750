import * as React from "react";
import { alpha } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import AppNavbar from "./components/AppNavbar";
import Header from "./components/Header";
import SideMenu from "./components/SideMenu";
import AppTheme from "../shared-theme/AppTheme";
import {
  // chartsCustomizations,
  dataGridCustomizations,
  datePickersCustomizations,
  treeViewCustomizations,
} from "./theme/customizations/index";
import chartsCustomizations from './theme/customizations/charts'
import MainGrid from "./components/MainGrid";
import axios from "axios";
import { useDispatch, useSelector } from "react-redux";
import { fetchLocations } from "../redux/actions/location";
import { useParams, useNavigate } from "react-router-dom";

const xThemeComponents = {
  ...chartsCustomizations,
  ...dataGridCustomizations,
  ...datePickersCustomizations,
  ...treeViewCustomizations,
};

export default function Dashboard(props) {
  const { locationId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const locationData = useSelector((state) => state.locations?.locations);

  React.useEffect(() => {
    dispatch(fetchLocations());
  }, [dispatch]);

  React.useEffect(() => {
    if (locationId) {
      // Store the location ID in localStorage
      localStorage.setItem("selectedLocationId", locationId);
      
      // If no locationId in URL but we have a selected location in localStorage,
      // redirect to that location's URL
    } else {
      const savedLocationId = localStorage.getItem("selectedLocationId");
      if (savedLocationId) {
        navigate(`/${savedLocationId}`);
      }
    }
  }, [locationId, navigate]);

  return (
    <AppTheme {...props} themeComponents={xThemeComponents}>
      <CssBaseline enableColorScheme />
      <Box sx={{ display: "flex" }}>
        <SideMenu />
        <AppNavbar />
        {/* Main content */}
        <Box
          component="main"
          sx={(theme) => ({
            flexGrow: 1,
            backgroundColor: theme.vars
              ? `rgba(${theme.vars.palette.background.defaultChannel} / 1)`
              : alpha(theme.palette.background.default, 1),
            overflow: "auto",
          })}
        >
          <Stack
            spacing={2}
            sx={{
              alignItems: "center",
              // mx: 3,
              pb: 5,
              mt: { xs: 8, md: 7 },
            }}
          >
            <Header contacts = "Home" />
            <MainGrid locationData = {locationData} selectedLocationId={locationId}/>
          </Stack>
        </Box>
      </Box> 
    </AppTheme>
  );
}
