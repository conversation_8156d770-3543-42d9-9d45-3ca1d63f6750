// import React, { useEffect, useState } from "react";
// import axios from "axios";

// const OpportunitiesList = () => {
//   const [opportunities, setOpportunities] = useState([]);
//   const [page, setPage] = useState(1);
//   const [limit] = useState(10);
//   const [total, setTotal] = useState(0);

//   useEffect(() => {
//     fetchOpportunities();
//   }, [page]); // Fetch data whenever page changes

//   const fetchOpportunities = async () => {
//     try {
//       const response = await axios.get(`http://localhost:5000/api/opportunities`, {
//         params: { limit, page },
//       });
//       setOpportunities(response.data.opportunities);
//       setTotal(response.data.total);
//     } catch (error) {
//       console.error("Error fetching opportunities:", error);
//     }
//   };

//   const totalPages = Math.ceil(total / limit);

//   return (
//     <div>
//       <h2>Pipeline List</h2>
//       <ul>
//         {opportunities?.map((opp) => (
//           <li key={opp.id}>{opp.name}</li>
//         ))}
//       </ul>

//       {/* Pagination Controls */}
//       <div>
//         <button onClick={() => setPage((prev) => Math.max(prev - 1, 1))} disabled={page === 1}>
//           Previous
//         </button>
//         <span> Page {page} of {totalPages} </span>
//         <button onClick={() => setPage((prev) => (prev < totalPages ? prev + 1 : prev))} disabled={page >= totalPages}>
//           Next
//         </button>
//       </div>
//     </div>
//   );
// };

// export default OpportunitiesList;

// src/components/OpportunitiesList.js
// src/components/OpportunitiesList.js
import React, { useState, useEffect } from 'react';
import { getPipelineData } from '../services/ghlService';

const OpportunitiesList = () => {
  const [pipelineData, setPipelineData] = useState({
    pipelines: [],
    opportunities: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedPipeline, setSelectedPipeline] = useState(null);

  // You can add this to your app state or get from user input
  const locationId = 't5r1YBZkxDCWUO2FyDkN';

  useEffect(() => {
    const fetchPipelineData = async () => {
      try {
        setLoading(true);
        const data = await getPipelineData(locationId);
        setPipelineData(data);

        // Set initial selected pipeline if available
        if (data.pipelines && data.pipelines.length > 0) {
          setSelectedPipeline(data.pipelines[0].id);
        }

        setLoading(false);
      } catch (err) {
        setError('Failed to fetch pipeline data: ' + (err.message || 'Unknown error'));
        setLoading(false);
      }
    };

    fetchPipelineData();
  }, [locationId]);

  // Filter opportunities by the selected pipeline
  const filteredOpportunities = selectedPipeline
    ? pipelineData.opportunities.filter(opp => opp.pipelineId === selectedPipeline)
    : pipelineData.opportunities;

  if (loading) return <div>Loading pipeline data...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="opportunities-list">
      <h2>Opportunities</h2>

      {/* Pipeline Selector */}
      {pipelineData.pipelines.length > 0 && (
        <div className="pipeline-selector">
          <label htmlFor="pipeline-select" sx={{
            '&.Mui-focused': {
              // backgroundColor: '#fff',
              backgroundColor: (theme) => theme.palette.background.paper,
              px: 0.5
            }
          }}>Select Pipeline: </label>
          <select
            id="pipeline-select"
            value={selectedPipeline || ''}
            onChange={(e) => setSelectedPipeline(e.target.value)}
          >
            {pipelineData.pipelines.map(pipeline => (
              <option key={pipeline.id} value={pipeline.id}>
                {pipeline.name}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Opportunities List */}
      {filteredOpportunities.length === 0 ? (
        <p>No opportunities found</p>
      ) : (
        <div className="opportunities-grid">
          {filteredOpportunities.map((opportunity) => (
            <div key={opportunity.id} className="opportunity-card">
              <h3>{opportunity.name || 'Unnamed Opportunity'}</h3>
              <div className="opportunity-details">
                <p><strong>Value:</strong> ${opportunity.value || 0}</p>
                <p><strong>Stage:</strong> {opportunity.stageName || 'N/A'}</p>
                <p><strong>Contact:</strong> {opportunity.contactName || 'N/A'}</p>
                <p><strong>Status:</strong> {opportunity.status || 'N/A'}</p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default OpportunitiesList;