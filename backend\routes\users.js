const express = require('express');
const router = express.Router();

// GET /search?locationId=...
router.get('/users-by-location', async (req, res) => {
    const authHeader = req.headers.authorization;
    const locationId = req.query.locationId;

    console.log('location query is:', locationId)
    if (!authHeader) {
        return res.status(401).json({ error: "Missing Authorization header" });
    }
    if (!locationId) {
        return res.status(400).json({ error: "Missing locationId query parameter" });
    }

    try {
        const response = await fetch(`https://services.leadconnectorhq.com/users/?locationId=${locationId}`, {
            method: "GET",
            headers: {
                Authorization: `Bearer ${authHeader}`,
                Version: "2021-04-15",
                Accept: "application/json",
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        res.json(data);
    } catch (error) {
        console.error("Error fetching users:", error);
        res.status(500).json({ error: "Failed to fetch users data" });
    }
});

module.exports = router;
