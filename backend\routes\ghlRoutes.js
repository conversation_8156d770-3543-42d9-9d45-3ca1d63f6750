// routes/ghlRoutes.js
const express = require('express');
const router = express.Router();
const { getContacts, getOpportunities,testGHLEndpoints , getPipelineData} = require('../controllers/ghlController');

// Define routes
router.get('/contacts', getContacts);
router.get('/opportunities', getOpportunities);
router.get('/test-endpoints', testGHLEndpoints);
// routes/ghlRoutes.js
router.get('/pipeline-data', getPipelineData);

// Add more routes as needed

module.exports = router;