const express = require("express");
const router = express.Router();
require("dotenv").config();

const app = express();
app.use(express.json());

const GHL_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6InQ1cjFZQlpreERDV1VPMkZ5RGtOIiwiY29tcGFueV9pZCI6IjA5bnRJc1JieDRnQXhjaGJIcWU3IiwidmVyc2lvbiI6MSwiaWF0IjoxNjg1NzI3MjA5NjYxLCJzdWIiOiJ1c2VyX2lkIn0.-UcbUyP-4nxElkvgSfm8s_78bk0cWb3A44B_vZj-29s";
const locationId = "t5r1YBZkxDCWUO2FyDkN";

// Get all opportunities
router.get("/opportunities", async (req, res) => {
  try {
    const { limit = 20, page = 1, startDate, endDate, status } = req.query;

    // Construct query parameters
    const queryParams = new URLSearchParams({
      locationId,
      limit: parseInt(limit, 10),
      page: parseInt(page, 10),
      ...(startDate && { startDate }),
      ...(endDate && { endDate }),
      ...(status && { status }),
    }).toString();

    const url = `https://rest.gohighlevel.com/v1/pipelines/kMJU2YtgfxG41dgErII8/opportunities?${queryParams}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${GHL_API_KEY}`,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    const data = await response.json();

    res.json({
      opportunities: data.opportunities,
      total: data.meta?.total || 0, // Handle missing meta data safely
      limit: parseInt(limit, 10),
      page: parseInt(page, 10),
    });
  } catch (error) {
    console.error("Error fetching opportunities:", error);
    res.status(500).json({ error: "Failed to fetch opportunities" });
  }
});

module.exports = router;
