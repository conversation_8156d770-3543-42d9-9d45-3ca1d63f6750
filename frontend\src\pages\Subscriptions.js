// import { useState, useEffect } from 'react';
// import axios from 'axios';

// function Subscriptions() {
//   const [mrrData, setMrrData] = useState(null);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState(null);
//   const apiKey = process.env.GHL_API_KEY;
//   useEffect(() => {
//     const fetchMRR = async () => {
//       try {

//         setLoading(true);
//         const response = await axios.get('http://localhost:5000/api/calculate-mrr', {
//         });
        
//         setMrrData(response.data);
//         setError(null);
//       } catch (error) {
//         setError('Failed to fetch MRR data: ' + (error.response?.data?.error || error.message));
//         console.error('Error:', error);
//       } finally {
//         setLoading(false);
//       }
//     };

//     fetchMRR();
//   }, []);

//   if (loading) return <div>Loading MRR data...</div>;
//   if (error) return <div className="error">{error}</div>;
  
//   return (
//     <div className="mrr-dashboard">
//       <h2>Monthly Recurring Revenue</h2>
//       <div className="mrr-value">${mrrData?.mrr.toFixed(2)}</div>
//       <div className="active-subscriptions">
//         Active Subscriptions: {mrrData?.activeSubscriptions}
//       </div>
//     </div>
//   );
// }

// export default Subscriptions;




// import React, { useState, useEffect } from 'react';
// import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar } from 'recharts';

// const Subscriptions = () => {
//   const [mrrData, setMrrData] = useState(null);
//   const [trendData, setTrendData] = useState(null);
//   const [period, setPeriod] = useState('monthly');
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState(null);

//   useEffect(() => {
//     const fetchMRRData = async () => {
//       try {
//         setLoading(true);
//         // Fetch the current MRR data
//         const response = await fetch('http://localhost:5000/api/mrr/direct');
        
//         if (!response.ok) {
//           throw new Error(`API responded with status ${response.status}`);
//         }
        
//         const data = await response.json();
//         setMrrData(data);
//         setLoading(false);
//       } catch (err) {
//         console.error('Error fetching MRR data:', err);
//         setError(err.message);
//         setLoading(false);
//       }
//     };

//     fetchMRRData();
//   }, []);

//   useEffect(() => {
//     const fetchTrendData = async () => {
//       try {
//         setLoading(true);
//         // Fetch trend data based on selected period
//         const response = await fetch(`http://localhost:5000/api/mrr/trend?period=${period}`);
        
//         if (!response.ok) {
//           throw new Error(`API responded with status ${response.status}`);
//         }
        
//         const data = await response.json();
//         setTrendData(data);
//         setLoading(false);
//       } catch (err) {
//         console.error(`Error fetching ${period} trend data:`, err);
//         setError(err.message);
//         setLoading(false);
//       }
//     };

//     fetchTrendData();
//   }, [period]);

//   const handlePeriodChange = (newPeriod) => {
//     setPeriod(newPeriod);
//   };

//   if (loading && !mrrData) {
//     return (
//       <div className="flex items-center justify-center h-64">
//         <div className="text-xl text-gray-600">Loading MRR data...</div>
//       </div>
//     );
//   }

//   if (error && !mrrData && !trendData) {
//     return (
//       <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
//         <strong className="font-bold">Error:</strong>
//         <span className="block sm:inline"> {error}</span>
//       </div>
//     );
//   }

//   // If we have data, render the dashboard
//   return (
//     <div className="bg-white rounded-lg shadow-lg p-6 max-w-6xl mx-auto">
//       <div className="mb-8">
//         <h1 className="text-3xl font-bold text-gray-800 mb-2">Monthly Recurring Revenue Dashboard</h1>
//         <p className="text-gray-600">
//           Track your business's recurring revenue performance
//         </p>
//       </div>

//       {/* MRR Overview Cards */}
//       {mrrData && (
//         <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
//           <div className="bg-blue-50 p-6 rounded-lg shadow">
//             <h2 className="text-xl font-semibold text-gray-800 mb-2">Current MRR</h2>
//             <div className="text-4xl font-bold text-blue-600">{mrrData.formattedMRR}</div>
//             {mrrData.mrrGrowth && (
//               <div className={`text-sm mt-2 ${parseFloat(mrrData.mrrGrowth) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
//                 {parseFloat(mrrData.mrrGrowth) >= 0 ? '↑' : '↓'} {Math.abs(parseFloat(mrrData.mrrGrowth))}% from previous month
//               </div>
//             )}
//           </div>
          
//           {trendData && (
//             <>
//               <div className="bg-green-50 p-6 rounded-lg shadow">
//                 <h2 className="text-xl font-semibold text-gray-800 mb-2">Average MRR</h2>
//                 <div className="text-4xl font-bold text-green-600">{trendData.formattedAverageMRR}</div>
//                 <div className="text-sm text-gray-600 mt-2">
//                   Based on {period} data
//                 </div>
//               </div>
              
//               <div className="bg-purple-50 p-6 rounded-lg shadow">
//                 <h2 className="text-xl font-semibold text-gray-800 mb-2">MRR Growth</h2>
//                 <div className={`text-4xl font-bold ${parseFloat(trendData.growthRate) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
//                   {parseFloat(trendData.growthRate) >= 0 ? '+' : ''}{trendData.growthRate}%
//                 </div>
//                 <div className="text-sm text-gray-600 mt-2">
//                   Over the {period} period
//                 </div>
//               </div>
//             </>
//           )}
//         </div>
//       )}

//       {/* Period Selection */}
//       <div className="flex justify-end mb-4">
//         <div className="inline-flex rounded-md shadow-sm" role="group">
//           <button
//             type="button"
//             className={`px-4 py-2 text-sm font-medium rounded-l-lg ${period === 'monthly' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'}`}
//             onClick={() => handlePeriodChange('monthly')}
//           >
//             Monthly
//           </button>
//           <button
//             type="button"
//             className={`px-4 py-2 text-sm font-medium ${period === 'quarterly' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'}`}
//             onClick={() => handlePeriodChange('quarterly')}
//           >
//             Quarterly
//           </button>
//           <button
//             type="button"
//             className={`px-4 py-2 text-sm font-medium rounded-r-lg ${period === 'yearly' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'}`}
//             onClick={() => handlePeriodChange('yearly')}
//           >
//             Yearly
//           </button>
//         </div>
//       </div>

//       {/* MRR Trend Chart */}
//       {trendData && trendData.data && (
//         <div className="bg-white p-6 rounded-lg shadow mb-8">
//           <h2 className="text-xl font-semibold text-gray-800 mb-4">MRR Trend</h2>
//           <div className="h-80">
//             <ResponsiveContainer width="100%" height="100%">
//               <LineChart
//                 data={trendData.data}
//                 margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
//               >
//                 <CartesianGrid strokeDasharray="3 3" />
//                 <XAxis dataKey="date" />
//                 <YAxis 
//                   tickFormatter={(value) => `$${value.toLocaleString()}`} 
//                   domain={['dataMin - 1000', 'dataMax + 1000']}
//                 />
//                 <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, 'MRR']} />
//                 <Legend />
//                 <Line 
//                   type="monotone" 
//                   dataKey="mrr" 
//                   stroke="#8884d8" 
//                   name="MRR" 
//                   activeDot={{ r: 8 }} 
//                   strokeWidth={2}
//                 />
//               </LineChart>
//             </ResponsiveContainer>
//           </div>
//         </div>
//       )}

//       {/* MRR Data Table */}
//       {mrrData && mrrData.data && (
//         <div className="bg-white rounded-lg shadow overflow-hidden">
//           <h2 className="text-xl font-semibold text-gray-800 p-4 border-b">MRR History</h2>
//           <div className="overflow-x-auto">
//             <table className="min-w-full divide-y divide-gray-200">
//               <thead className="bg-gray-50">
//                 <tr>
//                   <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
//                   <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRR</th>
//                 </tr>
//               </thead>
//               <tbody className="bg-white divide-y divide-gray-200">
//                 {mrrData.data.map((item, index) => (
//                   <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
//                     <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.date}</td>
//                     <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.formattedMRR}</td>
//                   </tr>
//                 ))}
//               </tbody>
//             </table>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// export default Subscriptions;


import React, { useState, useEffect } from 'react';
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 10000
});

function Subscriptions() {
  const [salesData, setSalesData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        const response = await api.get('/sales', {
          params: { limit: 10, page: 1 }
        });
        setSalesData(response.data);
        setError(null);
      } catch (err) {
        setError('Failed to fetch sales data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!salesData) return <div>No data available</div>;

  return (
    <div className="dashboard">
      <h1>Sales Dashboard</h1>
      
      <div className="metrics-panel">
        <div className="metric-card">
          <h3>Total Revenue</h3>
          <p>CA$ {salesData.monthlyMetrics["2025-03"]?.revenue || 0}</p>
        </div>
        
        <div className="metric-card">
          <h3>Monthly Recurring Revenue</h3>
          <p>CA$ {salesData.monthlyMetrics["2025-03"]?.mrr || 0}</p>
        </div>
        
        <div className="metric-card">
          <h3>New Opportunities</h3>
          <p>{salesData.monthlyMetrics["2025-03"]?.opportunityCount || 0}</p>
        </div>
        
        <div className="metric-card">
          <h3>Total Opportunities</h3>
          <p>{salesData.total || 0}</p>
        </div>
      </div>
      
      {/* Add more components to display the opportunities list, charts, etc. */}
    </div>
  );
}

export default Subscriptions;