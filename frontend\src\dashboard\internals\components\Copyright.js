import * as React from 'react';
import Link from '@mui/material/Link';
import Typography from '@mui/material/Typography';

export default function Copyright(props) {
  return (
    <Typography
      variant="body2"
      align="center"
      {...props}
      sx={[
        {
          color: 'text.secondary',
        },
        {
          '& a': {
            textDecoration: 'none'
          },
          '& a::before': {
            display: 'none'
          }
        },
        ...(Array.isArray(props.sx) ? props.sx : [props.sx]),
      ]}
    >
      {'Copyright © '}
      <Link color="inherit" href="https://www.gonano.com/" target="_blank" rel="noopener noreferrer">
        gonano
      </Link>&nbsp;
      {new Date().getFullYear()}
      {'.'}
    </Typography>
  );
}