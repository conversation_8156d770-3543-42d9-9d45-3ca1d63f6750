const connectDB = require("../config/db");

const initUserTable = async () => {
  const db = await connectDB();
  try {
    // Create table if not exists
    await db.execute(`
      CREATE TABLE IF NOT EXISTS custom_users (
        id VARCHAR(255) PRIMARY KEY,
        name VA<PERSON>HA<PERSON>(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL
      )
    `);

    // Add accessToken if not exists
    await db.execute(`
      ALTER TABLE custom_users ADD COLUMN accessToken TEXT
    `).catch(() => {}); // Ignore if already exists

    // Add refreshToken if not exists
    await db.execute(`
      ALTER TABLE custom_users ADD COLUMN refreshToken TEXT
    `).catch(() => {}); // Ignore if already exists

    console.log("✅ custom_users table created or updated successfully");
  } catch (err) {
    console.error("❌ Error in initUserTable:", err.message);
  }
};

const findUserByEmail = async (email) => {
  const db = await connectDB();
  const [rows] = await db.execute(
    "SELECT * FROM custom_users WHERE email = ?",
    [email]
  );
  return rows[0];
};

const createUser = async (id, name, email, password) => {
  const db = await connectDB();
  await db.execute(
    "INSERT INTO custom_users (id, name, email, password) VALUES (?, ?, ?, ?)",
    [id, name, email, password]
  );
  //   console.log("Inserting user =>", id, name, email, password);
};

const saveTokens = async (email, accessToken, refreshToken) => {
  const db = await connectDB();
  await db.execute(
    "UPDATE custom_users SET accessToken = ?, refreshToken = ? WHERE email = ?",
    [accessToken, refreshToken, email]
  );
};

module.exports = {
  initUserTable,
  findUserByEmail,
  createUser,
  saveTokens,
};
