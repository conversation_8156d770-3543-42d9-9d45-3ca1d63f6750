import * as React from 'react';
import { styled } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import Breadcrumbs, { breadcrumbsClasses } from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';
import NavigateNextRoundedIcon from '@mui/icons-material/NavigateNextRounded';
import { useNavigate, useLocation } from 'react-router';

const StyledBreadcrumbs = styled(Breadcrumbs)(({ theme }) => ({
  margin: theme.spacing(1, 0),
  [`& .${breadcrumbsClasses.separator}`]: {
    color: (theme.vars || theme).palette.action.disabled,
    margin: 1,
  },
  [`& .${breadcrumbsClasses.ol}`]: {
    alignItems: 'center',
  },
}));

export default function NavbarBreadcrumbs({ title }) {
  const navigate = useNavigate();
  const location = useLocation();
  const selectedLocationId = localStorage.getItem('selectedLocationId');

  const isOnLocationPage = location.pathname === `/${selectedLocationId}`;

  const handleClick = (event) => {
    event.preventDefault();
    navigate(`/${selectedLocationId}`);
  };

  return (
    <StyledBreadcrumbs
      aria-label="breadcrumb"
      separator={<NavigateNextRoundedIcon fontSize="small" />}
    >
      <Link
        color={isOnLocationPage ? 'text.primary' : 'inherit'}
        href={`/${selectedLocationId}`}
        onClick={handleClick}
        sx={{ cursor: 'pointer' }}
      >
        Dashboard
      </Link>
      <Typography variant="body1" sx={{ color: 'text.primary', fontWeight: 600 }}>
        {title}
      </Typography>
    </StyledBreadcrumbs>
  );
}
