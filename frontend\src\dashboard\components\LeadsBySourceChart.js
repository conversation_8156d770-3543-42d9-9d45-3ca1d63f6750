import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  Typography,
  Box,
  useTheme,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@mui/material";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from "recharts";
import { useParams } from "react-router-dom";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { subDays } from "date-fns";

export default function LeadsBySourceChart() {
  const theme = useTheme();
  const { locationId } = useParams(); // Get locationId from URL
  const today = new Date();
  const [startDate, setStartDate] = useState(subDays(today, 7)); // Default to 7 days ago
  const [endDate, setEndDate] = useState(today);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [data, setData] = useState([]);
  const [totalLeads, setTotalLeads] = useState(0);

  // Colors for each segment
  const COLORS = [
    "#2196F3",
    "#4CAF50",
    "#FF9800",
    "#9C27B0",
    "#F44336",
    "#00BCD4",
    "#795548",
    "#607D8B",
    "#E91E63",
    "#3F51B5",
  ];

  // Handle date changes
  const handleStartDateChange = (newDate) => {
    setStartDate(newDate);
    // If end date is before new start date, update end date
    if (newDate && endDate && newDate > endDate) {
      setEndDate(newDate);
    }
  };

  const handleEndDateChange = (newDate) => {
    setEndDate(newDate);
  };

  // Function to fetch leads data
  const fetchLeadsData = async () => {
    // Don't fetch if dates are not valid
    if (!startDate || !endDate || startDate > endDate) {
      return;
    }

    setLoading(true);
    setError(null);

    const authToken = localStorage.getItem("locationAccessTokenContact");

    if (!authToken) {
      setError("No authorization token found");
      setLoading(false);
      return;
    }

    if (!locationId) {
      setError("No location ID found in URL");
      setLoading(false);
      return;
    }

    // Create date range with proper time handling
    const dateRange = {
      gte: new Date(startDate.setHours(0, 0, 0, 0)).toISOString(),
      lte: new Date(endDate.setHours(23, 59, 59, 999)).toISOString(),
    };

    try {
      const response = await fetch("http://localhost:5000/api/leadGenrateV2", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          locationId: locationId,
          pageLimit: 500,
          page: 1,
          filters: [
            {
              field: "dateAdded",
              operator: "range",
              value: dateRange,
            },
          ],
          sort: [
            {
              field: "dateAdded",
              direction: "desc",
            },
          ],
        }),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const result = await response.json();

      // Process sources data
      if (result.sources) {
        const sourcesArray = Object.entries(result.sources)
          .map(([name, value]) => ({
            name: name.charAt(0).toUpperCase() + name.slice(1), // Capitalize first letter
            value: parseInt(value),
            displayName: formatSourceName(name),
          }))
          .sort((a, b) => b.value - a.value); // Sort by value descending

        // Calculate percentages
        const total = sourcesArray.reduce((sum, item) => sum + item.value, 0);
        setTotalLeads(total);

        const processedData = sourcesArray.map((item, index) => ({
          ...item,
          percentage: total > 0 ? Math.round((item.value / total) * 100) : 0,
          color: COLORS[index % COLORS.length],
        }));

        // Limit to top 5 sources and group the rest as "Other"
        let finalData = processedData;
        if (processedData.length > 5) {
          const top5 = processedData.slice(0, 5);
          const otherValue = processedData
            .slice(5)
            .reduce((sum, item) => sum + item.value, 0);
          const otherPercentage =
            total > 0 ? Math.round((otherValue / total) * 100) : 0;

          finalData = [
            ...top5,
            {
              name: "Other",
              displayName: "Other",
              value: otherValue,
              percentage: otherPercentage,
              color: COLORS[5],
            },
          ];
        }

        setData(finalData);
      }

      setLoading(false);
    } catch (err) {
      console.error("Error fetching leads data:", err);
      setError(err.message);
      setLoading(false);
    }
  };

  // Helper function to format source names
  const formatSourceName = (source) => {
    const nameMap = {
      facebook: "Facebook",
      website: "Website",
      "google my business": "Google My Business",
      "facebook form lead": "Facebook Ads",
      booking_widget: "Booking Widget",
      unknown: "Unknown",
    };

    return (
      nameMap[source.toLowerCase()] ||
      source
        .split(/[-_]/)
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    );
  };

  // Fetch data when component mounts or when submit is clicked
  useEffect(() => {
    // Only fetch on initial mount with default dates
    if (startDate && endDate && locationId) {
      fetchLeadsData();
    }
  }, [locationId]); // Only depend on locationId for initial load

  const liveTime = new Date();
  const tomorrow = new Date(
    liveTime.getFullYear(),
    liveTime.getMonth(),
    liveTime.getDate() + 1
  );

  const handleSubmit = () => {
    const isInvalidDate =
      !startDate ||
      !endDate ||
      startDate > endDate ||
      startDate >= tomorrow ||
      endDate >= tomorrow;

    if (isInvalidDate) return;

    fetchLeadsData();
  };

  // Custom label line function
  const renderLabelLine = (props) => {
    const { cx, cy, midAngle, outerRadius, index } = props;
    const RADIAN = Math.PI / 180;
    const radius = outerRadius + 30;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill={data[index]?.color || "#000"}
        textAnchor={x > cx ? "start" : "end"}
        dominantBaseline="central"
        fontSize="14"
      >
        {`${data[index]?.displayName} ${data[index]?.percentage}%`}
      </text>
    );
  };

  // Custom legend component
  const CustomLegend = () => (
    <Box sx={{ mt: 4 }}>
      {data.map((entry) => (
        <Box
          key={entry.name}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            mb: 1,
            px: 2,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", flex: 1 }}>
            <Box
              sx={{
                width: 12,
                height: 12,
                borderRadius: "50%",
                backgroundColor: entry.color,
                mr: 1.5,
              }}
            />
            <Typography variant="body2" color="text.secondary">
              {entry.displayName}
            </Typography>
          </Box>
          <Typography variant="body2" fontWeight="medium">
            {entry.value.toLocaleString()}
          </Typography>
        </Box>
      ))}
    </Box>
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Card
        sx={{
          backgroundColor: (theme) =>
            theme.palette.mode === "dark" ? theme.palette.grey[900] : "#ffffff",
          boxShadow:
            theme.palette.mode === "dark"
              ? "0 1px 3px rgba(0,0,0,0.5)"
              : "0 1px 3px rgba(0,0,0,0.12)",
          borderRadius: 2,
          height: "100%",
        }}
      >
        <CardContent>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" component="h2" fontWeight="bold">
              Leads
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
              Total leads in selected period: : {totalLeads.toLocaleString()}
            </Typography>

            <Box
              sx={{
                display: "flex",
                gap: 2,
                flexWrap: "wrap",
                alignItems: "flex-start",
              }}
            >
              <DatePicker
                label="From Date"
                value={startDate}
                onChange={handleStartDateChange}
                maxDate={today}
                disabled={loading}
                slotProps={{
                  textField: {
                    sx: { minWidth: 180, flex: 1 },
                    helperText: "Select start date",
                  },
                }}
              />

              <DatePicker
                label="To Date"
                value={endDate}
                onChange={handleEndDateChange}
                minDate={startDate}
                maxDate={today}
                disabled={loading}
                slotProps={{
                  textField: {
                    sx: { minWidth: 180, flex: 1 },
                    helperText: "Must be after start date",
                  },
                }}
              />

              <Button
                variant={
                  loading ||
                  !startDate ||
                  !endDate ||
                  startDate > endDate ||
                  startDate >= tomorrow ||
                  endDate >= tomorrow
                    ? "outlined"
                    : "contained"
                }
                onClick={handleSubmit}
                disabled={
                  loading ||
                  !startDate ||
                  !endDate ||
                  startDate > endDate ||
                  startDate >= tomorrow ||
                  endDate >= tomorrow
                }
                sx={{
                  minWidth: 100,
                  height: 56,
                  alignSelf: "flex-start",
                  cursor:
                    loading ||
                    !startDate ||
                    !endDate ||
                    startDate > endDate ||
                    startDate >= tomorrow ||
                    endDate >= tomorrow
                      ? "not-allowed"
                      : "pointer",
                }}
              >
                Submit
              </Button>
            </Box>
          </Box>

          {loading && (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: 400,
              }}
            >
              <CircularProgress />
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {!loading && !error && data.length > 0 && (
            <>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={data}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    // label={renderLabelLine}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {data.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => value.toLocaleString()}
                    contentStyle={{
                      backgroundColor: theme.palette.background.paper,
                      border: `1px solid ${theme.palette.divider}`,
                      borderRadius: 4,
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>

              <CustomLegend />
            </>
          )}

          {!loading && !error && data.length === 0 && (
            <Box sx={{ textAlign: "center", py: 4 }}>
              <Typography variant="body1" color="text.secondary">
                No leads found for the selected date range
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </LocalizationProvider>
  );
}
