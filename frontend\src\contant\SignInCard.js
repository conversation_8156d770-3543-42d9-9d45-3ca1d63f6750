import * as React from "react";
import axios from "axios";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import MuiCard from "@mui/material/Card";
import Checkbox from "@mui/material/Checkbox";
import Divider from "@mui/material/Divider";
import FormLabel from "@mui/material/FormLabel";
import FormControl from "@mui/material/FormControl";
import FormControlLabel from "@mui/material/FormControlLabel";
import Link from "@mui/material/Link";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { styled } from "@mui/material/styles";
import gonanologo from "../static/images/svgviewer-output.svg";
import { GoogleIcon, FacebookIcon, SitemarkIcon } from "./CustomIcons";
import ForgotPassword from "../pages/ForgotPassword";
import InputAdornment from "@mui/material/InputAdornment";
import IconButton from "@mui/material/IconButton";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import Snackbar from "@mui/material/Snackbar";
import Alert from "@mui/material/Alert";
import { ToastContainer, toast } from "react-toastify";
import SubAccountModal from "../components/SubAccountModal";

const Card = styled(MuiCard)(({ theme }) => ({
  display: "flex",
  flexDirection: "column",
  alignSelf: "center",
  width: "100%",
  padding: theme.spacing(4),
  gap: theme.spacing(2),
  boxShadow:
    "hsla(220, 30%, 5%, 0.05) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.05) 0px 15px 35px -5px",
  [theme.breakpoints.up("sm")]: {
    width: "450px",
  },
  ...theme.applyStyles("dark", {
    boxShadow:
      "hsla(220, 30%, 5%, 0.5) 0px 5px 15px 0px, hsla(220, 25%, 10%, 0.08) 0px 15px 35px -5px",
  }),
}));

export default function SignInCard() {
  const [emailError, setEmailError] = useState(false);
  const [emailErrorMessage, setEmailErrorMessage] = useState("");
  const [passwordError, setPasswordError] = useState(false);
  const [passwordErrorMessage, setPasswordErrorMessage] = useState("");
  const [open, setOpen] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loginError, setLoginError] = useState("");
  const [userData, setUserData] = useState({ email: "", password: "" });
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState("");
  const [showSubAccountModal, setShowSubAccountModal] = useState(false);
  const navigate = useNavigate();

  const base_url = process.env.REACT_APP_API_BASE_URL;
  const NEW_API_URL = process.env.REACT_APP_NEW_BASE_URL2;

  console.log("base_url", base_url);

  // ✅ Handle OAuth callback results when component mounts
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.get("ghl_connected") === "true") {
      console.log("GHL connection successful!");
      toast.success(
        "GoHighLevel connected successfully! Please login again to access your dashboard."
      );
      setSuccessMessage(
        "GoHighLevel connected successfully! Please login again to access your dashboard."
      );

      // Clear the URL parameter
      window.history.replaceState({}, document.title, window.location.pathname);
    }

    if (urlParams.get("error")) {
      const error = urlParams.get("error");
      console.error("GHL connection error:", error);

      let errorMessage = "GoHighLevel connection failed";
      if (error === "no_code") {
        errorMessage = "Authorization was cancelled or failed";
      } else if (error === "auth_failed") {
        errorMessage = "Failed to connect to GoHighLevel";
      }

      toast.error(errorMessage);
      setLoginError(errorMessage);

      // Clear the URL parameter
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleChange = (e) => {
    setUserData({ ...userData, [e.target.name]: e.target.value });

    // Clear errors when user starts typing
    if (e.target.name === "email") {
      setEmailError(false);
      setEmailErrorMessage("");
    }
    if (e.target.name === "password") {
      setPasswordError(false);
      setPasswordErrorMessage("");
    }
    setLoginError("");
  };

  const handleSnackbarClose = () => {
    setSuccessMessage("");
  };

  const email = userData.email;
  const password = userData.password;

  // ✅ UPDATED handleSubmit function with automated OAuth flow
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setLoginError(""); // Clear previous errors

    // Basic validation
    if (!email) {
      setEmailError(true);
      setEmailErrorMessage("Email is required");
      setIsSubmitting(false);
      return;
    }
    if (!password) {
      setPasswordError(true);
      setPasswordErrorMessage("Password is required");
      setIsSubmitting(false);
      return;
    }

    try {
      console.log("Attempting login with:", { email });

      // ✅ Call your updated login API endpoint
      const response = await axios.post(`${NEW_API_URL}/api/login`, {
        email,
        password,
      });

      const responseData = response.data;
      console.log("Login response:", responseData);

      // ✅ Handle successful login
      if (
        responseData.loginSuccess ||
        responseData.message === "Login successful" ||
        responseData.message === "Login successful - GHL connection required"
      ) {
        // ✅ Store user authentication tokens
        if (responseData.user) {
          localStorage.setItem(
            "JWTUsertoken",
            responseData.user.userAccessToken
          );
          localStorage.setItem(
            "userRefreshToken",
            responseData.user.userRefreshToken
          );

          // Combine the user data and token into a single object
          const currentUser = {
            id: responseData.user.id,
            name: responseData.user.name,
            email: responseData.user.email,
            token: responseData.user.userAccessToken,
          };
          localStorage.setItem("currentUser", JSON.stringify(currentUser));

          console.log("User tokens stored successfully");
        }

        // ✅ Check if GHL connection is needed
        if (responseData.needsGHLConnection) {
          console.log("GHL authorization required");

          // Show user-friendly message
          toast.info(
            "Login successful! You will now be redirected to connect your GoHighLevel account.",
            {
              position: "top-center",
              autoClose: 3000,
            }
          );

          // Small delay to show the toast, then redirect
          setTimeout(() => {
            console.log("Redirecting to GHL OAuth:", responseData.ghlAuthUrl);
            // Redirect to GHL OAuth - this will automatically capture the code
            // window.location.href = responseData.ghlAuthUrl;
          }, 2000);

          return;
        }

        // ✅ GHL is already connected - store GHL data
        if (responseData.ghlData) {
          const ghlData = responseData.ghlData;

          console.log("Storing GHL data:", ghlData);

          // Store GHL tokens and data
          localStorage.setItem("ghlAccessToken", ghlData.accessToken);
          if (ghlData.refreshToken) {
            localStorage.setItem("refreshToken", ghlData.refreshToken);
          }
          if (ghlData.tokenType) {
            localStorage.setItem("token_type", ghlData.tokenType);
          }
          if (ghlData.expiresIn) {
            localStorage.setItem("expires_in", ghlData.expiresIn);
          }
          if (ghlData.scope) {
            localStorage.setItem("scope", JSON.stringify(ghlData.scope));
          }
          if (ghlData.userType) {
            localStorage.setItem("userType", ghlData.userType);
          }
          if (ghlData.companyId) {
            localStorage.setItem("companyId", ghlData.companyId);
          }
          if (ghlData.locationId) {
            localStorage.setItem("locationId", ghlData.locationId);
          }
          if (ghlData.uniqueId) {
            localStorage.setItem("uniqueId", ghlData.uniqueId);
          }
          toast.success("Login successful!", {
            position: "top-center",
            autoClose: 2000,
          });

          // Navigate to dashboard
          setTimeout(() => {
            navigate("/select-sub-account");
          }, 1500);
        }
      } else {
        // Handle unexpected response structure
        console.error("Unexpected response structure:", responseData);
        toast.error("Login failed: Unexpected response from server");
        setLoginError("Unexpected response from server");
      }
    } catch (error) {
      console.error(
        "Login failed:",
        error.response?.data?.message || error.message
      );

      // Handle different types of errors
      let errorMessage = "Something went wrong";

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 404) {
        errorMessage = "User not found";
      } else if (error.response?.status === 401) {
        errorMessage = "Invalid credentials";
      } else if (error.response?.status === 500) {
        errorMessage = "Server error. Please try again later.";
      } else if (error.code === "NETWORK_ERROR") {
        errorMessage = "Network error. Please check your connection.";
      } else {
        errorMessage = error.message || "Something went wrong";
      }

      toast.error(`Login failed: ${errorMessage}`, {
        position: "top-center",
        autoClose: 4000,
      });

      setLoginError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <ToastContainer />
      <Card
        variant="outlined"
        sx={{
          p: { xs: 2, md: 4 },
        }}
      >
        <Box sx={{ display: { xs: "flex", md: "none" } }}>
          <img
            src={gonanologo}
            alt="gonanoLogo"
            style={{
              objectFit: "cover",
              height: "40px",
              width: "140px",
              marginLeft: "-10px",
            }}
          />
        </Box>
        <Typography
          component="h1"
          variant="h4"
          sx={{ width: "100%", fontSize: "clamp(2rem, 10vw, 2.15rem)" }}
        >
          Sign in
        </Typography>

        {/* {loginError && (
          <Alert severity="error" sx={{ mt: 1 }}>
            {loginError}
          </Alert>
        )}

        {successMessage && (
          <Alert severity="success" sx={{ mt: 1 }}>
            {successMessage}
          </Alert>
        )} */}

        <Box
          component="form"
          onSubmit={handleSubmit}
          noValidate
          sx={{
            display: "flex",
            flexDirection: "column",
            width: "100%",
            gap: 2,
          }}
        >
          <FormControl>
            <FormLabel htmlFor="email">Email</FormLabel>
            <TextField
              error={emailError}
              helperText={emailErrorMessage}
              value={userData.email}
              onChange={handleChange}
              id="email"
              type="email"
              name="email"
              placeholder="<EMAIL>"
              autoComplete="email"
              required
              fullWidth
              variant="outlined"
              color={emailError ? "error" : "primary"}
              disabled={isSubmitting}
            />
          </FormControl>
          <FormControl>
            <Box sx={{ display: "flex", justifyContent: "space-between" }}>
              <FormLabel htmlFor="password">Password</FormLabel>
              <Link
                component="button"
                type="button"
                onClick={handleClickOpen}
                variant="body2"
                sx={{ alignSelf: "baseline" }}
                disabled={isSubmitting}
              >
                Forgot your password?
              </Link>
            </Box>
            <TextField
              error={passwordError}
              helperText={passwordErrorMessage}
              name="password"
              value={userData.password}
              onChange={handleChange}
              placeholder="••••••"
              type={showPassword ? "text" : "password"}
              id="password"
              autoComplete="current-password"
              required
              fullWidth
              variant="outlined"
              color={passwordError ? "error" : "primary"}
              disabled={isSubmitting}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={togglePasswordVisibility}
                      edge="end"
                      disabled={isSubmitting}
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
                sx: {
                  "& .MuiIconButton-sizeMedium.css-spzvdl-MuiButtonBase-root-MuiIconButton-root":
                    {
                      paddingLeft: "25px",
                      height: "35px",
                      margin: "6px 0px",
                      border: "none",
                    },
                },
              }}
            />
          </FormControl>
          <ForgotPassword open={open} handleClose={handleClose} />
          <Button
            type="submit"
            fullWidth
            // variant="contained"
             variant={isSubmitting ? "outlined" : "contained"}
            disabled={isSubmitting}
            sx={{
              marginTop: "1rem",
              minHeight: "48px",
            }}
          >
            {isSubmitting ? "Signing  in..." : "Sign in"}
          </Button>
          <Typography sx={{ textAlign: "center" }}>
            Don&apos;t have an account?{" "}
            <span>
              <Link
                href="/sign-up"
                variant="body2"
                sx={{ alignSelf: "center" }}
              >
                Sign up
              </Link>
            </span>
          </Typography>
        </Box>
        {/* <Divider>or</Divider> */}
        {/* <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          <Button
            fullWidth
            variant="outlined"
            onClick={handleGoogleSignIn}
            startIcon={<GoogleIcon />}
          >
            Sign in with Google
          </Button>
          <Button
            fullWidth
            variant="outlined"
            onClick={handleFacebookSignIn}
            startIcon={<FacebookIcon />}
          >
            Sign in with Facebook
          </Button>
        </Box> */}
      </Card>
      {/* <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }}
      >
        <Alert variant="filled" severity="success" sx={{ width: "100%" }}>
          {successMessage}
        </Alert>
      </Snackbar> */}
      {showSubAccountModal && <SubAccountModal />}
    </>
  );
}
