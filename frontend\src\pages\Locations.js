import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { fetchLocations } from '../redux/actions/location';

const Locations = () => {
  const dispatch = useDispatch();
  const locationData = useSelector(state => state.locations);
  
  const { loading, locations, error } = locationData;

  useEffect(() => {
    dispatch(fetchLocations());
  }, [dispatch]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-6">Locations</h1>
        
        {loading && <p>Loading...</p>}
        {error && <p className="text-red-500">Error: {error}</p>}
        
        {locations && locations.length > 0 && (
          <ul className="divide-y divide-gray-200">
            {locations.map(location => (
              <li key={location.id} className="py-3">
                {location.name}
              </li>                 
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default Locations;

