const express = require("express");
const router = express.Router();
require("dotenv").config();

// Ensure native fetch is available (Node.js v18+)
const fetch = global.fetch;

// CORS Middleware
router.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  if (req.method === "OPTIONS") {
    return res.sendStatus(200);
  }
  next();
});

// GET all pipelines
router.get("/pipelines", async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({ error: "Missing or invalid authorization token" });
    }

    const apiKey = authHeader.split(" ")[1];
    const url = "https://rest.gohighlevel.com/v1/pipelines/";

    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return res.status(response.status).json({
        error: "Failed to fetch pipelines",
        details: data,
      });
    }

    console.log(`Fetched ${data.pipelines?.length || 0} pipelines`);
    res.json(data);
  } catch (error) {
    console.error("Error fetching pipelines:", error);
    res.status(500).json({ error: "Failed to fetch pipeline data" });
  }
});

// GET all opportunities for a pipeline (with pagination)
router.get("/pipelines/:pipelineId/all-opportunities", async (req, res) => {
  try {
    const { pipelineId } = req.params;
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({ error: "Missing or invalid authorization token" });
    }

    const apiKey = authHeader.split(" ")[1];

    let url = `https://rest.gohighlevel.com/v1/pipelines/${pipelineId}/opportunities?limit=100`;
    let allOpportunities = [];
    let totalCount = 0;

    let startAfterId, startAfter, nextPageUrl;

    while (url) {
      console.log(`Fetching opportunities from: ${url}`);

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${apiKey}`,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        return res.status(response.status).json({
          error: "Failed to fetch opportunities",
          details: data,
        });
      }

      const { opportunities = [], meta = {} } = data;
      totalCount = meta.total || totalCount;
      startAfterId = meta.startAfterId;
      startAfter = meta.startAfter;
      nextPageUrl = meta.nextPageUrl;

      allOpportunities = [...allOpportunities, ...opportunities];

      console.log(`Fetched ${opportunities.length} opportunities, total so far: ${allOpportunities.length}`);

      // Stop if no next page or we’ve fetched everything
      if (!nextPageUrl || allOpportunities.length >= totalCount) break;

      // Update URL with pagination parameters
      url = `https://rest.gohighlevel.com/v1/pipelines/${pipelineId}/opportunities?limit=100&startAfterId=${startAfterId}&startAfter=${startAfter}`;
    }

    // Append pipelineId for reference
    const opportunitiesWithId = allOpportunities.map((opp) => ({
      ...opp,
      pipelineId,
    }));

    console.log(`Successfully fetched all ${opportunitiesWithId.length} opportunities`);

    res.json({
      opportunities: opportunitiesWithId,
      meta: {
        total: totalCount,
        fetched: opportunitiesWithId.length,
      },
    });
  } catch (error) {
    console.error("Error fetching opportunities:", error);
    res.status(500).json({ error: "Failed to fetch all pipeline opportunities" });
  }
});

module.exports = router;
