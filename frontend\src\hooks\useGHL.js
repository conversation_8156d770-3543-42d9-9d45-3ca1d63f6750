// src/hooks/useGHL.js
import { useContext } from 'react';
import { GHLContext } from '../context/GHLContext';
import { ghlService } from '../services/ghlService';

export const useGHL = () => {
  const { token, setLocations } = useContext(GHLContext);

  const fetchLocations = async () => {
    try {
      const data = await ghlService.getLocations(token);
      setLocations(data.locations);
    } catch (error) {
      console.error('Error fetching locations:', error);
    }
  };

  return { fetchLocations };
};