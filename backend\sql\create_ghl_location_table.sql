-- Create ghl_location_data table
CREATE TABLE IF NOT EXISTS ghl_location_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    location_id VARCHAR(255) NOT NULL UNIQUE,
    company_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255),
    
    -- Basic location info
    name VARCHAR(500),
    address TEXT,
    city VARCHAR(255),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(50),
    timezone VARCHAR(100),
    
    -- Contact person info
    first_name VARCHAR(255),
    last_name VA<PERSON>HA<PERSON>(255),
    email VARCHAR(255),
    phone VARCHAR(100),
    
    -- Business info
    business_name VARCHAR(500),
    business_address TEXT,
    business_city VARCHAR(255),
    business_state VARCHAR(100),
    business_country VARCHAR(100),
    business_postal_code VARCHAR(50),
    business_timezone VARCHAR(100),
    business_logo_url TEXT,
    business_email VARCHAR(255),
    
    -- Social media
    facebook_url TEXT,
    google_plus VARCHAR(500),
    linkedin VARCHAR(500),
    foursquare VARCHAR(500),
    twitter VARCHAR(500),
    yelp VARCHAR(500),
    instagram VARCHAR(500),
    youtube VARCHAR(500),
    pinterest VARCHAR(500),
    blog_rss VARCHAR(500),
    google_places_id VARCHAR(255),
    
    -- Settings (stored as JSON)
    settings JSON,
    
    -- Additional fields
    logo_url TEXT,
    domain VARCHAR(255),
    automatic_mobile_app_invite BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    date_added DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for better performance
    INDEX idx_location_id (location_id),
    INDEX idx_company_id (company_id),
    INDEX idx_user_id (user_id),
    INDEX idx_date_added (date_added),
    INDEX idx_created_at (created_at),
    INDEX idx_business_name (business_name),
    INDEX idx_city_state (city, state)
);

-- Add comments for documentation
ALTER TABLE ghl_location_data 
COMMENT = 'Stores GHL (Go High Level) location data with business information, social media links, and settings'; 