// middleware/authMiddleware.js
const validateAPIKey = (req, res, next) => {
    const apiKey = req.headers['x-api-key'];
    
    if (!apiKey) {
      return res.status(401).json({ message: 'API key is required' });
    }
    
    // Validate the API key (implement your validation logic)
    // For example, check against an environment variable or database
    
    // If valid, proceed
    next();
  };
  
  module.exports = { validateAPIKey };